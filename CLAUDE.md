# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Overview

This is Flow IDE, a customized version of Visual Studio Code developed by ZTE Corporation. It's based on VSCode's open-source codebase with significant AI-powered features and enhancements built on top.

## Key Commands

### Build and Development
- `npm run compile` - Full compilation including React components and TypeScript
- `npm run watch` - Watch mode for development (includes React watch)
- `npm run buildreact` - Build React components in codeseek extension
- `npm run watchreact` - Watch mode for React components only
- `npm run watch-client` - Watch TypeScript client code
- `npm run watch-extensions` - Watch extensions code

### Linting and Quality
- `npm run eslint` - Run ESLint
- `npm run stylelint` - Run style linting
- `npm run hygiene` - Code hygiene checks
- `npm run precommit` - Pre-commit checks

### Testing
- `npm run test-browser` - Run browser tests (includes Playwright install)
- `npm run test-node` - Run Node.js tests
- `npm run smoketest` - Run smoke tests

### Specialized Tasks
- `npm run download-codebase` - Download external codebase dependencies
- `npm run compile-cli` - Compile CLI components
- `npm run compile-web` - Compile web components

## Architecture

### Core Structure
- **src/vs/**: Main VSCode source directory
  - **base/**: Foundation classes (browser, common, node)
  - **platform/**: Platform services and abstractions
  - **editor/**: Monaco editor implementation
  - **workbench/**: Main workbench implementation
    - **contrib/**: Feature contributions including the main AI extension

### CodeSeek AI Extension
The main AI features are implemented in `src/vs/workbench/contrib/codeseek/`:

- **browser/**: Frontend AI services
  - Chat thread management (`chatThreadService.ts`)
  - AI agents (`agent/` directory)
  - Code editing services (`editCodeService.ts`)
  - Sidebar interface (`sidebarPane.ts`, `sidebarActions.ts`)
  - Settings management (`codeseekSettingsPane.ts`)
  - React components (`react/` directory)

- **common/**: Shared AI service interfaces
  - LLM message handling (`llmMessageService.ts`)
  - Settings service (`codeseekSettingsService.ts`)
  - File and codebase services

- **electron-main/**: Backend AI services
  - LLM proxy and message channels
  - Update services
  - Main process AI coordination

### React Integration
AI UI components are built with React and located in:
- `src/vs/workbench/contrib/codeseek/browser/react/`
- Build process: `node build.js` (custom build script)
- Watch process: `node build.js --watch`

### Key AI Features
- Chat interface with thread management
- Code completion and suggestions
- Inline diff editing
- Symbol search with ctags/clangd integration
- Context gathering and code analysis
- Multi-LLM provider support (Anthropic, OpenAI, Mistral, etc.)

## Development Workflow

1. **Initial Setup**: Run `npm install` (triggers codebase download)
2. **Development**: Use `npm run watch` for full development mode
3. **React Changes**: Use `npm run watchreact` for React-only changes
4. **Quality Checks**: Run `npm run eslint` and `npm run hygiene` before commits
5. **Testing**: Use appropriate test commands based on changes

## Important Notes

- The codebase downloads external dependencies via `downloadCodebase()` task
- React components require separate build step before TypeScript compilation
- AI services use both main process (electron-main) and renderer process (browser) architecture
- Extension contributions are registered via `.contribution.ts` files
- CSS is located in `media/` subdirectories within each contribution