import { Emitter, Event } from '../../../../../base/common/event.js';
import { IServerChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { generateUuid } from '../../../../../base/common/uuid.js';
import { CreateRepoConfig, EnsureIndexParams, EventCodebaseOnProgressParams, FileReadRequest, FileReadResponse, FileReadSizeRequest, FileReadSizeResponse, IndexEndEvent, IndexStartEvent, MainResumeRemoteIndexParams, MainUpdateIndexParams, ResumeRemoteIndexParams, UpdateIndexParams } from '../../common/codebaseTypes.js';
import { ICodebaseRemoteMainService, RemoteEnsureIndexParams } from '../codebaseRemoteMainService.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { LogLevel } from '../../../../../platform/log/common/log.js';
import { URI } from '../../../../../base/common/uri.js';

export class CodebaseChannel implements IServerChannel {

	private readonly remoteMessageEmitters = {
		onIndexStart: new Emitter<IndexStartEvent>(),
		onIndexEnd: new Emitter<IndexEndEvent>(),
		onProgress: new Emitter<EventCodebaseOnProgressParams>(),
	};

	private readonly fileReadRequestEmitter = new Emitter<FileReadRequest>();
	private readonly fileReadSizeRequestEmitter = new Emitter<FileReadSizeRequest>();
	private readonly pendingFileReads = new Map<string, { resolve: Function; reject: Function; timeout?: NodeJS.Timeout }>();

	constructor(
		private readonly remoteMainService: ICodebaseRemoteMainService,
		private readonly logger: ICodeseekLogger,
	) {
		this.logger.setLevel(LogLevel.Info);
		this.remoteMainService.setFileContentReader(this.readRemoteFileContent.bind(this));
		this.remoteMainService.setFileContentSizeReader(this.readRemoteFileContentSize.bind(this));
	}

	listen(_: unknown, event: string): Event<any> {
		switch (event) {
			case 'onIndexStart_remote':
				return this.remoteMessageEmitters.onIndexStart.event;
			case 'onIndexEnd_remote':
				return this.remoteMessageEmitters.onIndexEnd.event;
			case 'onProgress_remote':
				return this.remoteMessageEmitters.onProgress.event;
			case 'fileReadRequest':
				return this.fileReadRequestEmitter.event;
			case 'fileReadSizeRequest':
				return this.fileReadSizeRequestEmitter.event;
			default:
				throw new Error('Unknown event');
		}
	}

	call(_: unknown, command: string, params: any): any {
		switch (command) {
			case 'ensureRemoteIndex':
				return this._callEnsureIndex(params);
			case 'deleteRemoteIndex':
				return this.remoteMainService.deleteRepoIndex(params);
			case 'getRemoteResults':
				return this.remoteMainService.getResults(params);
			case 'startRemoteCodebase':
				return this.remoteMainService.startCodebaseProcess();
			case 'stopRemoteCodebase':
				return this.remoteMainService.stopCodebaseProcess();
			case 'pauseRemoteIndex':
				return this.remoteMainService.pauseRemoteIndex(params);
			case 'resumeRemoteIndex':
				return this._callResumeIndex(params);
			case 'updateRemoteIndex':
				return this._callUpdateIndex(params);
			case 'queryCallChain':
				return this.remoteMainService.queryCallChain(params);
			case 'queryDependency':
				return this.remoteMainService.queryDependency(params);
			case 'fileReadResponse':
				return this.handleFileReadResponse(params);
			case 'fileReadSizeResponse':
				return this.handleFileReadSizeResponse(params);
			default:
				throw new Error(`Unknown command: ${command}`);
		}
	}

	private _callEnsureIndex(params: EnsureIndexParams & CreateRepoConfig): Promise<void> {
		const params_: RemoteEnsureIndexParams = {
			...params,
			onProgress: (p: EventCodebaseOnProgressParams) => {
				this.remoteMessageEmitters.onProgress.fire(p);
			}
		};
		return this.remoteMainService.ensureIndex(params_);
	}

	private _callResumeIndex(params: ResumeRemoteIndexParams): Promise<boolean> {
		const params_: MainResumeRemoteIndexParams = {
			...params,
			onProgress: (p: EventCodebaseOnProgressParams) => {
				this.remoteMessageEmitters.onProgress.fire(p);
			}
		};
		return this.remoteMainService.resumeRemoteIndex(params_);
	}

	private _callUpdateIndex(params: UpdateIndexParams): Promise<boolean> {
		const params_: MainUpdateIndexParams = {
			...params,
			onProgress: (p: EventCodebaseOnProgressParams) => {
				this.remoteMessageEmitters.onProgress.fire(p);
			}
		};
		return this.remoteMainService.updateRemoteIndex(params_);
	}

	private async readRemoteFileContent(uri: URI): Promise<string> {
		const requestId = generateUuid();
		const authority = uri.authority || 'default';

		this.logger.info(`[CodebaseChannel] Sending file read request, ID: ${requestId}, URI: ${JSON.stringify(uri)}, Authority: ${authority}`);

		return new Promise((resolve, reject) => {
			// 设置超时
			const timeout = setTimeout(() => {
				if (this.pendingFileReads.has(requestId)) {
					this.pendingFileReads.delete(requestId);
					this.logger.error(`[CodebaseChannel] File read timeout for ID: ${requestId}, Authority: ${authority}, URI: ${JSON.stringify(uri)}`);
					reject(new Error(`File content read timeout for ${uri.path || uri.toString()}`));
				}
			}, 60000); // 60秒超时

			this.pendingFileReads.set(requestId, { resolve, reject, timeout });
			this.logger.debug(`[CodebaseChannel] Stored pending request, total pending: ${this.pendingFileReads.size}`);

			// 发送请求到渲染进程，包含权限信息
			try {
				const fileReadRequest: FileReadRequest = { requestId, uri, authority };
				this.fileReadRequestEmitter.fire(fileReadRequest);
			} catch (error) {
				this.logger.error(`[CodebaseChannel] Failed to fire event for ID: ${requestId}, Authority: ${authority}, error: ${error}`);
				const pending = this.pendingFileReads.get(requestId);
				if (pending && pending.timeout) {
					clearTimeout(pending.timeout);
				}
				this.pendingFileReads.delete(requestId);
				reject(error);
				return;
			}
		});
	}

	private handleFileReadResponse(params: FileReadResponse): void {
		this.logger.debug(`[CodebaseChannel] Received file read response for ID: ${params.requestId}, hasResult: ${!!params.result}, hasError: ${!!params.error}`);

		const pending = this.pendingFileReads.get(params.requestId);
		if (pending) {
			// 清理超时定时器
			if (pending.timeout) {
				clearTimeout(pending.timeout);
			}
			this.pendingFileReads.delete(params.requestId);

			if (params.error) {
				this.logger.debug(`[CodebaseChannel] Resolving with error for ID: ${params.requestId}: ${params.error}`);
				pending.reject(new Error(params.error));
			} else {
				this.logger.debug(`[CodebaseChannel] Resolving with result for ID: ${params.requestId}, content length: ${params.result?.length || 0}`);
				pending.resolve(params.result);
			}
		} else {
			this.logger.warn(`[CodebaseChannel] No pending request found for ID: ${params.requestId} - may have timed out or been resolved already`);
		}
	}

	private async readRemoteFileContentSize(uri: URI): Promise<number> {
		const requestId = generateUuid();
		const authority = uri.authority || 'default';

		return new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error(`File content size read timeout for ${uri.path || uri.toString()}`));
			}, 60000);

			this.pendingFileReads.set(requestId, { resolve, reject, timeout });

			// 发送请求到渲染进程，包含权限信息
			try {
				const fileReadSizeRequest: FileReadSizeRequest = { requestId, uri, authority };
				this.fileReadSizeRequestEmitter.fire(fileReadSizeRequest);
			} catch (error) {
				this.logger.error(`[CodebaseChannel] Failed to fire event for ID: ${requestId}, Authority: ${authority}, error: ${error}`);
				const pending = this.pendingFileReads.get(requestId);
				if (pending && pending.timeout) {
					clearTimeout(pending.timeout);
				}
				this.pendingFileReads.delete(requestId);
				reject(error);
				return;
			}
		});
	}

	private handleFileReadSizeResponse(params: FileReadSizeResponse): void {
		this.logger.debug(`[CodebaseChannel] Received file read size response for ID: ${params.requestId}, hasSize: ${!!params.size}, hasError: ${!!params.error}`);

		const pending = this.pendingFileReads.get(params.requestId);
		if (pending) {
			if (pending.timeout) {
				clearTimeout(pending.timeout);
			}
			this.pendingFileReads.delete(params.requestId);

			if (params.error) {
				this.logger.debug(`[CodebaseChannel] Resolving with error for ID: ${params.requestId}: ${params.error}`);
				pending.reject(new Error(params.error));
			} else {
				this.logger.debug(`[CodebaseChannel] Resolving with size for ID: ${params.requestId}, size: ${params.size}`);
				pending.resolve(params.size);
			}
		} else {
			this.logger.warn(`[CodebaseChannel] No pending request found for ID: ${params.requestId} - may have timed out or been resolved already`);
		}
	}
}
