import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { EnsureIndexParams, QueryContextData, CreateRepoData, RepoSnapshotData, QueryContextParams, EventCodebaseOnProgressParams, RepoState, DeleteRepoIndexParams, CreateRepoParams, CreateRepoConfig, PauseRemoteIndexParams, MainResumeRemoteIndexParams, MainUpdateIndexParams, QueryDependencyParams, QueryCallChainParams, QueryDependencyResult, QueryCallChainResult, SearchReqParams } from '../common/codebaseTypes.js';
import * as path from '../../../../base/common/path.js';
import { URI } from '../../../../base/common/uri.js';
import { defaultExcludeFiles, defaultExcludeFolders, IDirectoryNode, IFileNode } from '../common/codeseekFileService.js';
import * as crypto from 'crypto';
import { IFileService } from '../../../../platform/files/common/files.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { osType } from '../browser/helpers/systemInfo.js';
import os from 'os';
import { ILifecycleMainService } from '../../../../platform/lifecycle/electron-main/lifecycleMainService.js';
import { countProjectFiles, shouldExclude, shouldExcludePath } from './utils/common.js';
import { filenameToVscodeLanguage } from '../common/helpers/detectLanguage.js';
import { IEnvironmentMainService } from '../../../../platform/environment/electron-main/environmentMainService.js';
import { CodebaseRunner, CodebaseUrl } from './codebase/codebaseRunner.js';
import { LogLevel } from '../../../../platform/log/common/log.js';
import { IUacLoginService } from './uac/uacLoginService.js';

enum CodebaseCode {
	RepoCreateSuccess = 0,
	RepoCreateFailed = 1,
	RepoAlreadyExist = 2
}

export type RemoteEnsureIndexParams = EnsureIndexParams & CreateRepoConfig & {
	onProgress: (p: EventCodebaseOnProgressParams) => void;
}

// 批处理结果统计接口
interface BatchProcessResult {
	totalFiles: number;
	successCount: number;
	failedCount: number;
	skippedCount: number;
	failedFiles: Array<{ path: string; error: string; attempts: number }>;
}

export interface ICodebaseRemoteMainService {
	readonly _serviceBrand: undefined;

	startCodebaseProcess(): Promise<boolean>;
	stopCodebaseProcess(): Promise<void>;
	checkCodebaseHealth(): Promise<boolean>;

	ensureIndex(params: RemoteEnsureIndexParams): Promise<void>;
	getResults(params: SearchReqParams): Promise<QueryContextData | undefined>;
	deleteRepoIndex(params: DeleteRepoIndexParams): Promise<boolean>;
	pauseRemoteIndex(params: PauseRemoteIndexParams): Promise<boolean>;
	resumeRemoteIndex(params: MainResumeRemoteIndexParams): Promise<boolean>;
	updateRemoteIndex(params: MainUpdateIndexParams): Promise<boolean>;
	queryDependency(params: QueryDependencyParams): Promise<QueryDependencyResult[]>;
	queryCallChain(params: QueryCallChainParams): Promise<QueryCallChainResult | undefined>;
	setFileContentReader(reader: (uri: URI) => Promise<string>): void;
	setFileContentSizeReader(reader: (uri: URI) => Promise<number>): void;
}

export const ICodebaseRemoteMainService = createDecorator<ICodebaseRemoteMainService>('codebaseRemoteMainService');
export class CodebaseRemoteMainService extends Disposable implements ICodebaseRemoteMainService {
	readonly _serviceBrand: undefined;
	private allState: Record<string, RepoState> = {};
	private codebaseRunner: CodebaseRunner | null = null;
	private isIndexPaused = false;
	private readonly OPTIMAL_BATCH_SIZE = 2; // 最优批次大小
	private fileContentReader: ((uri: URI) => Promise<string>) | undefined;
	private fileContentSizeReader: ((uri: URI) => Promise<number>) | undefined;

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IFileService private readonly fileService: IFileService,
		@IProductService private readonly productService: IProductService,
		@ILifecycleMainService private readonly lifecycleMainService: ILifecycleMainService,
		@IEnvironmentMainService private readonly _envMainService: IEnvironmentMainService,
		@IUacLoginService private readonly uacLoginService: IUacLoginService,
	) {
		super();

		this.logger.setLevel(LogLevel.Info);

		this.initialize().catch(error => {
			this.logger.error(`初始化失败: ${error.message}`);
			throw error;
		});
		this._register({
			dispose: () => {
				this.codebaseRunner?.stop().catch(err => {
					this.logger.error(`Error stopping codebase process during dispose: ${err.message}`);
				});
			}
		});
		this.lifecycleMainService.onWillShutdown(e => this.codebaseRunner?.shutdown());
		if (this._isDevMode()) {
			this.lifecycleMainService.onWillLoadWindow(e => this.codebaseRunner?.shutdown());
		}
	}

	private async initialize(): Promise<void> {
		const codebasePath = await this.getCodebasePath();
		if (!codebasePath) {
			throw new Error('Codebase executable not found');
		}
		const isDebug = this.isDebugMode();
		this.codebaseRunner = new CodebaseRunner(codebasePath, isDebug, this.logger);
	}

	private _isDevMode(): boolean {
		return !this._envMainService.isBuilt;
	}

	setFileContentReader(reader: (uri: URI) => Promise<string>): void {
		this.fileContentReader = reader;
	}

	setFileContentSizeReader(reader: (uri: URI) => Promise<number>): void {
		this.fileContentSizeReader = reader;
	}

	async getResults(params: SearchReqParams): Promise<QueryContextData | undefined> {
		if (!(params.repoUri.fsPath in this.allState)) {
			this.logger.info(`the ${params.repoUri.fsPath} repo is not exist, skip get results`);
			return undefined;
		}

		if (!this.isRepoIndexCompleted(params.repoUri.fsPath)) {
			this.logger.info(`the ${params.repoUri.fsPath} repo is not completed, skip get results`);
			return undefined;
		}

		if (params.llm.provider === 'default') {
			params.llm.baseUrl = params.llm.model === 'AI-IDE-R1-32B' ? 'http://***********:30804/ai-ide-service-1/v1/v1' :
				params.llm.model === 'AI-IDE-FastApply' ? 'http://***********:30804/test-service-3/v1/v1' :
					params.llm.baseUrl;
		}

		const userInfo = await this.uacLoginService.getUserInfo();
		const userId = userInfo?.userId;

		let isTry = false;
		const repoId = this.allState[params.repoUri.fsPath].repoId;
		try {
			const requestData: QueryContextParams = {
				llm: params.llm,
				query: params.userQuery,
				repoId: repoId,
			};
			const queryContextData: QueryContextData = await this.codebaseRunner?.post(CodebaseUrl.queryContext, requestData, userId);
			return queryContextData;
		} catch (error) {
			if (error.name === 'AbortError' && !isTry) {
				isTry = true;
				await this.codebaseRunner?.restart();
				return await this.getResults(params);
			}
			this.logger.error(`Failed to get results for ${params.repoUri.fsPath} repo`);
			return undefined;
		}
	}

	private async getRepoId(repoUri: URI): Promise<string | null> {
		const body = {
			repoPath: repoUri.fsPath,
			repoName: repoUri.path.split('/').pop()
		};
		try {
			const userInfo = await this.uacLoginService.getUserInfo();
			const userId = userInfo?.userId;
			const response = await this.codebaseRunner?.post(`${CodebaseUrl.queryRepoIndex}`, body, userId, true, false);
			if (response.code === CodebaseCode.RepoCreateSuccess || response.code === CodebaseCode.RepoAlreadyExist) {
				return (response.data as CreateRepoData).repoId;
			}
		} catch (error) {
			this.logger.error(`Failed to get repo id for ${repoUri.fsPath} repo: ${error}`);
			throw error;
		}
		return null;
	}

	private async getFileContentSize(uri: URI): Promise<number> {
		if (uri.scheme === 'vscode-remote') {
			return await this.fileContentSizeReader!(uri);
		}
		return await this.fileService.stat(uri).then(stat => stat.size);
	}

	private async getFileContentAndHash(uri: URI): Promise<{ content: string, hashcode: string }> {
		const maxRetries = 3;
		let lastError: Error | undefined;

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				if (uri.scheme === 'vscode-remote') {
					this.logger.debug(`Attempting to read remote file (attempt ${attempt}/${maxRetries}): ${uri.path}`);

					const result = await this.fileContentReader!(uri);
					if (!result) {
						throw new Error(`Failed to read file ${uri.path} content - empty result`);
					}
					const hashcode = crypto.createHash('sha256').update(result).digest('hex');
					this.logger.debug(`Successfully read remote file on attempt ${attempt}, content length: ${result.length}, hash: ${hashcode.substring(0, 8)}...`);
					return { content: result, hashcode };
				} else {
					const content = await this.fileService.readFile(uri);
					const contentStr = content.value.toString();
					const hashcode = crypto.createHash('sha256').update(contentStr).digest('hex');
					return { content: contentStr, hashcode };
				}
			} catch (error) {
				lastError = error as Error;
				this.logger.error(`Failed to read file ${uri.path} (attempt ${attempt}/${maxRetries}):`, error);

				if (attempt < maxRetries) {
					// 等待一段时间后重试，递增延迟
					const delay = attempt * 2000; // 2秒, 4秒, 6秒
					this.logger.info(`Retrying file read in ${delay}ms...`);
					await new Promise(resolve => setTimeout(resolve, delay));
				}
			}
		}

		throw lastError || new Error(`Failed to read file ${uri.path} after ${maxRetries} attempts`);
	}

	async ensureIndex(params: RemoteEnsureIndexParams): Promise<void> {
		if (!['file', 'vscode-remote'].includes(params.repoUri.scheme)) {
			throw new Error(`Invalid URI scheme: ${params.repoUri.scheme}. Expected 'file' or 'vscode-remote'.`);
		}
		this.logger.info(`ensure index for ${params.repoUri.fsPath}`);
		try {
			// 创建Codebase repo
			const repoData = await this.createRepo(params);
			if (!repoData) {
				this.logger.error(`Failed to create repo for ${params.repoUri.fsPath} repo`);
				return;
			}

			// 获取Codebase repo snapshot
			const repoSnapshotData = await this.getRepoSnapshot(repoData.repoId);
			if (!repoSnapshotData) {
				this.logger.error(`Failed to get repo snapshot for ${repoData.repoId} repo`);
				return;
			}

			if (repoData.isComplete) {
				this.logger.info(`the ${params.repoUri.fsPath} repo is already complete, skip ensure index`);
				params.onProgress({ repoUri: params.repoUri, progress: 1, SyncedFileNumber: repoSnapshotData.repoSnapshot.size });
				this.onRepoIndexCompleted(params.repoUri.fsPath, repoData.repoId, repoData.repoName!, repoData.repoPath!);
			} else {
				this.onRepoIndexRunning(params.repoUri.fsPath, repoData.repoId, repoData.repoName!, repoData.repoPath!);
			}

			// 同步Codebase repo file index
			await this.syncRepoFileIndex(params, repoData, repoSnapshotData);

		} catch (error) {
			this.logger.error(`Failed to ensure index for ${params.repoUri.fsPath} repo`);
			throw error;
		}
	}

	private async createRepo(params: RemoteEnsureIndexParams): Promise<CreateRepoData | null> {
		const createRepoData: CreateRepoParams = {
			repoPath: params.repoUri.fsPath,
			repoName: params.repoUri.path.split('/').pop()!,
			config: {
			}
		};

		if (params.clangd) {
			createRepoData.config.clangd = params.clangd;
		}

		const userInfo = await this.uacLoginService.getUserInfo();
		const userId = userInfo?.userId;

		// 失败，尝试5次，每次间隔500ms
		for (let i = 0; i < 5; i++) {
			try {
				const repoData: CreateRepoData = await this.codebaseRunner?.post(CodebaseUrl.createRepo, createRepoData, userId);
				this.onRepoIndexRunning(params.repoUri.fsPath, repoData.repoId, repoData.repoName!, repoData.repoPath!);
				return repoData;
			} catch (error) {
				this.logger.error(`Failed to create repo for ${params.repoUri.fsPath} repo, attempt ${i + 1}`);
				await new Promise(resolve => setTimeout(resolve, 500 * (i + 1)));
			}
		}
		return null;
	}

	private async deleteRepo(repoId: string, repoPath: string) {
		const userInfo = await this.uacLoginService.getUserInfo();
		const userId = userInfo?.userId;
		await this.codebaseRunner?.post(CodebaseUrl.deleteRepoIndex, { repoId }, userId);
		delete this.allState[repoPath];
		this.logger.info(`the ${repoPath} repo index delete complete`);
	}

	private async syncRepoFileIndex(
		params: RemoteEnsureIndexParams,
		repoData: CreateRepoData,
		repoSnapshotData: RepoSnapshotData,
	) {
		this.logger.info(`${params.repoUri.fsPath} repo start streaming file index processing`);

		if (!params.projectStructure?.root) {
			this.logger.info(`the ${params.repoUri.fsPath} repo project structure is empty, delete repo index`);
			await this.deleteRepo(repoData.repoId, params.repoUri.fsPath);
			params.onProgress({ repoUri: params.repoUri, progress: 0, SyncedFileNumber: 0 });
			return;
		}

		const totalFiles = await countProjectFiles(params.projectStructure!.root!, defaultExcludeFolders, defaultExcludeFiles);
		this.logger.info(`the ${params.repoUri.fsPath} repo total files: ${totalFiles}`);
		if (totalFiles === 0) {
			this.logger.info(`the ${params.repoUri.fsPath} repo no files need to be processed`);
			params.onProgress({ repoUri: params.repoUri, progress: 0, SyncedFileNumber: 0 });
			return;
		}

		const progress = repoSnapshotData.repoSnapshot.size / totalFiles;
		params.onProgress({ repoUri: params.repoUri, progress, SyncedFileNumber: repoSnapshotData.repoSnapshot.size });

		if (this.isRepoIndexPaused(params.repoUri.fsPath)) {
			this.logger.info(`${params.repoUri.fsPath} repo is already paused, skip ensure index`);
			return;
		}

		this.isIndexPaused = false;

		await this.streamProcessFileIndex(params, repoData.repoId, repoSnapshotData, totalFiles);

		if (!this.isRepoIndexPaused(params.repoUri.fsPath)) {
			const userInfo = await this.uacLoginService.getUserInfo();
			const userId = userInfo?.userId;
			await this.codebaseRunner?.post(CodebaseUrl.noticeRepoIndexCreateComplete, { repoId: repoData.repoId }, userId);
			this.logger.info(`the ${params.repoUri.fsPath} repo index create complete`);
			this.onRepoIndexCompleted(params.repoUri.fsPath);
		}
	}

	private async streamProcessFileIndex(
		params: RemoteEnsureIndexParams,
		repoId: string,
		repoSnapshotData: RepoSnapshotData,
		totalFiles: number
	): Promise<void> {
		const MAX_FILES = 100000;

		let totalProcessedFiles = 0; // 实际处理的文件数
		let totalScannedFiles = 0; // 已扫描的文件数
		let alreadyIndexedCount = repoSnapshotData.repoSnapshot.size; // 已索引文件数

		let fileBatch: Array<{ fileNode: IFileNode; operation: 'create' | 'update' }> = [];

		const processedPaths = new Set<string>();

		// 使用队列进行广度优先搜索，更平衡的处理顺序
		const queue: Array<IDirectoryNode | IFileNode> = [params.projectStructure!.root!];

		this.logger.info(`开始流式处理文件索引，预估总文件数: ${totalFiles}，已索引: ${alreadyIndexedCount}`);

		while (queue.length > 0 && totalScannedFiles < MAX_FILES) {
			// 检查是否被暂停
			if (this.isRepoIndexPaused(params.repoUri.fsPath)) {
				this.allState[params.repoUri.fsPath].projectStructure = params.projectStructure;
				if (params.clangd) {
					this.allState[params.repoUri.fsPath].clangd = params.clangd;
				}
				this.isIndexPaused = true;
				this.updateProgress(params, totalScannedFiles, totalProcessedFiles, totalFiles, alreadyIndexedCount);
				return;
			}

			const currentNode = queue.shift();
			if (!currentNode) continue;

			const nodePath = currentNode.uri.path;
			if (processedPaths.has(nodePath)) {
				continue;
			}
			processedPaths.add(nodePath);

			if (currentNode.type === 'file') {
				const fileNode = currentNode as IFileNode;
				if (shouldExclude(fileNode.name, defaultExcludeFiles)) {
					continue;
				}

				const relativePath = path.relative(params.repoUri.path, fileNode.uri.path);
				if (shouldExcludePath(relativePath, defaultExcludeFiles)) {
					continue;
				}
				totalScannedFiles++;

				try {
					const fileSize = await this.getFileContentSize(fileNode.uri);
					if (fileSize > 1024 * 1024 * 10) {
						this.logger.warn(`the ${fileNode.uri.path} file content size greater than 10MB, skip create file index`);
						totalProcessedFiles++;
						continue;
					}

					const { hashcode } = await this.getFileContentAndHash(fileNode.uri);
					const indexStatus = this.getIndexStatus(relativePath, hashcode, repoSnapshotData);

					switch (indexStatus) {
						case 'unindexed':
							fileBatch.push({ fileNode, operation: 'create' });
							break;
						case 'updating':
							fileBatch.push({ fileNode, operation: 'update' });
							repoSnapshotData.repoSnapshot.delete(relativePath);
							break;
						default:
							repoSnapshotData.repoSnapshot.delete(relativePath);
							break;
					}

					// 批量处理文件
					if (fileBatch.length >= this.OPTIMAL_BATCH_SIZE) {
						const batchProcessed = await this.processBatchFiles(fileBatch, repoId, params.repoUri);
						totalProcessedFiles += batchProcessed.successCount;
						fileBatch = [];
					}

					// 实时进度更新
					this.updateProgress(params, totalScannedFiles, totalProcessedFiles, totalFiles, alreadyIndexedCount);

				} catch (error) {
					this.logger.error(`处理文件失败 ${fileNode.uri.path}:`, error);
				}
			} else if (currentNode.type === 'directory') {
				const dirNode = currentNode as IDirectoryNode;
				if (shouldExcludePath(dirNode.uri.path, defaultExcludeFolders)) {
					continue;
				}
				if (dirNode.children && dirNode.children.length > 0) {
					queue.push(...dirNode.children);
				}
			}
		}

		if (fileBatch.length > 0) {
			const batchProcessed = await this.processBatchFiles(fileBatch, repoId, params.repoUri);
			totalProcessedFiles += batchProcessed.successCount;
		}

		alreadyIndexedCount -= repoSnapshotData.repoSnapshot.size;

		this.handleDeletions(repoSnapshotData, repoId);

		// 最终进度更新
		this.updateProgress(params, totalScannedFiles, totalProcessedFiles, totalFiles, alreadyIndexedCount, true);

		if (totalScannedFiles >= MAX_FILES) {
			this.logger.warn(`文件处理限制为 ${MAX_FILES} 个文件以防止内存问题`);
		}
	}

	/**
	 * 优化的进度更新计算
	 */
	private updateProgress(
		params: RemoteEnsureIndexParams,
		scannedFiles: number,
		processedFiles: number,
		totalFiles: number,
		alreadyIndexedCount: number,
		isFinal: boolean = false
	): void {
		// 更准确的进度计算
		const totalWorkDone = alreadyIndexedCount + processedFiles;
		const scanningProgress = Math.min(scannedFiles / totalFiles, 1.0);
		const processingProgress = totalWorkDone / totalFiles;

		// 综合进度：85% 权重给处理进度，15% 权重给扫描进度
		let overallProgress = processingProgress * 0.85 + scanningProgress * 0.15;

		// 最终状态设置为 1.0，否则限制最大值为 0.98
		if (isFinal) {
			overallProgress = 1.0;
		} else {
			overallProgress = Math.min(overallProgress, 0.98);
		}

		params.onProgress({
			repoUri: params.repoUri,
			progress: overallProgress,
			SyncedFileNumber: totalWorkDone
		});
	}

	/**
	 * 删除index repo中存在且不在工程中的文件
	 */
	private async handleDeletions(
		repoSnapshotData: RepoSnapshotData,
		repoId: string
	): Promise<void> {

		// 查找需要删除的文件（在快照中但不在扫描到的文件中）
		for (const [relativePath, _] of repoSnapshotData.repoSnapshot) {
			try {
				await this.deleteFileIndex(relativePath, repoId, false);
			} catch (error) {
				this.logger.error(`Failed to delete file index for ${relativePath}:`, error);
			}
		}
	}

	/**
	 * 批量文件处理
	 */
	private async processBatchFiles(
		fileBatch: Array<{ fileNode: IFileNode; operation: 'create' | 'update' }>,
		repoId: string,
		repoUri: URI
	): Promise<BatchProcessResult> {

		const result: BatchProcessResult = {
			totalFiles: fileBatch.length,
			successCount: 0,
			failedCount: 0,
			skippedCount: 0,
			failedFiles: []
		};

		if (fileBatch.length === 0) return result;

		try {
			const promises: Promise<{ success: boolean; error?: any; relativePath: string }>[] = [];

			for (const { fileNode, operation } of fileBatch) {
				const promise = this.processFileIndex(fileNode, repoId, operation, repoUri);
				promises.push(promise);
			}

			const results = await Promise.allSettled(promises);

			for (const promiseResult of results) {
				if (promiseResult.status === 'fulfilled') {
					const { success, error, relativePath } = promiseResult.value;
					if (success) {
						result.successCount++;
					} else {
						result.failedCount++;
						result.failedFiles.push({
							path: relativePath,
							error: error?.message || '未知错误',
							attempts: 1
						});
					}
				} else {
					result.failedCount++;
					result.failedFiles.push({
						path: '未知文件',
						error: promiseResult.reason?.message || '处理失败',
						attempts: 1
					});
				}
			}

			return result;
		} catch (error) {
			this.logger.error(`Failed to process batch files:`, error);
			return result;
		}
	}

	/**
	 * 处理单个文件索引（创建或更新）
	 */
	private async processFileIndex(
		fileNode: IFileNode,
		repoId: string,
		operation: 'create' | 'update',
		repoUri: URI
	): Promise<{ success: boolean; error?: any; relativePath: string }> {
		const relativePath = path.relative(repoUri.path, fileNode.uri.path);

		try {
			const fileSize = await this.getFileContentSize(fileNode.uri);
			if (fileSize > 1024 * 1024 * 10) {
				this.logger.warn(`the ${fileNode.uri.path} file content size greater than 10MB, skip create file index`);
				return { success: true, relativePath };
			}

			const { content, hashcode } = await this.getFileContentAndHash(fileNode.uri);
			const fileIndexData = {
				repoId,
				relativePath,
				content,
				hashcode,
				language: fileNode.language,
				fileType: repoUri.scheme === 'vscode-remote' ? 'remote' : 'local'
			};

			const userInfo = await this.uacLoginService.getUserInfo();
			const userId = userInfo?.userId;

			const timeout = content.length > 1024 * 1024 ? 240 * 1000 : 90 * 1000;
			if (operation === 'create') {
				await this.codebaseRunner?.post(CodebaseUrl.createFileIndex, fileIndexData, userId, false, true, timeout);
			} else {
				await this.codebaseRunner?.post(CodebaseUrl.updateFileIndex, fileIndexData, userId, false, true, timeout);
			}

			return { success: true, relativePath };
		} catch (error) {
			this.logger.error(`Failed to ${operation} file index for ${fileNode.uri.path}:`, error);
			return { success: false, error, relativePath };
		}
	}

	/**
	 * 删除单个文件索引
	 */
	private async deleteFileIndex(relativePath: string, repoId: string, isDirectory: boolean = false): Promise<void> {
		try {
			const userInfo = await this.uacLoginService.getUserInfo();
			const userId = userInfo?.userId;
			const data = { repoId, relativePath, isDirectory };
			await this.codebaseRunner?.post(CodebaseUrl.deleteFileIndex, data, userId, true);
		} catch (error) {
			this.logger.error(`Failed to delete file index for ${relativePath}:`, error);
		}
	}

	private async getRepoSnapshot(repoId: string): Promise<RepoSnapshotData | null> {
		try {
			const data = await this.codebaseRunner?.get(`${CodebaseUrl.queryRepoSnapshot}/${repoId}`, false);

			const repoSnapshot = new Map<string, string>();
			if (data && Array.isArray(data.repoSnapshot)) {
				data.repoSnapshot.forEach((item: { relativePath: string; hashcode: string }) => {
					repoSnapshot.set(item.relativePath, item.hashcode);
				});
			}

			return { repoSnapshot };
		} catch (error) {
			this.logger.error(`Failed to get repo snapshot for ${repoId} repo`);
			return null;
		}
	}


	async deleteRepoIndex(params: DeleteRepoIndexParams): Promise<boolean> {
		let isTry = false;
		try {
			if (!(params.repoUri.fsPath in this.allState)) {
				this.logger.info(`the ${params.repoUri.fsPath} repo is not exist, skip delete remote index`);
				return true;
			}

			if (this.isRepoIndexRunning(params.repoUri.fsPath)) {
				this.onRepoIndexPaused(params.repoUri.fsPath);
			} else {
				this.isIndexPaused = true;
			}

			while (!this.isIndexPaused) {
				await new Promise(resolve => setTimeout(resolve, 200));
			}
			const repoId = await this.getRepoId(params.repoUri);
			if (!repoId) {
				this.logger.error(`repo not found, skip delete remote index`);
				return true;
			}

			await this.deleteRepo(repoId, params.repoUri.fsPath);
			return true;
		} catch (error) {
			if (error.name === 'AbortError' && !isTry) {
				isTry = true;
				await this.codebaseRunner?.restart();
				return await this.deleteRepoIndex(params);
			}
			this.logger.error(`Failed to delete remote index for ${params.repoUri.fsPath} repo`);
			return false;
		}
	}

	async pauseRemoteIndex(params: PauseRemoteIndexParams): Promise<boolean> {
		try {
			if (!this.allState[params.repoUri.fsPath]) {
				this.logger.warn(`the ${params.repoUri.fsPath} repo is not exist, skip pause remote index`);
				return true;
			}
			this.logger.info(`the ${params.repoUri.fsPath} repo is paused, stop create index`);
			if (this.isRepoIndexRunning(params.repoUri.fsPath)) {
				this.onRepoIndexPaused(params.repoUri.fsPath);
			} else {
				this.isIndexPaused = true;
			}
			while (!this.isIndexPaused) {
				await new Promise(resolve => setTimeout(resolve, 200));
			}
			this.logger.info(`the ${params.repoUri.fsPath} repo index pause complete`);
			return true;
		} catch (error) {
			this.logger.error(`Failed to pause remote index for ${params.repoUri.fsPath} repo`);
			return false;
		}
	}

	async resumeRemoteIndex(params: MainResumeRemoteIndexParams): Promise<boolean> {
		try {
			if (!(params.repoUri.fsPath in this.allState)) {
				this.logger.error(`Repo not found in state, cannot resume: ${params.repoUri.fsPath}`);
				return false;
			}

			if (!this.isRepoIndexPaused(params.repoUri.fsPath)) {
				this.logger.info(`Repo is not in paused state, current state: ${this.allState[params.repoUri.fsPath].status}`);
				return true;
			}

			this.logger.info(`Resumed indexing for repo: ${params.repoUri.fsPath}`);

			const repoState = this.allState[params.repoUri.fsPath];
			const ensureParams: RemoteEnsureIndexParams = {
				repoUri: params.repoUri,
				projectStructure: repoState.projectStructure!,
				onProgress: params.onProgress,
			};
			if (repoState.clangd) {
				ensureParams.clangd = repoState.clangd;
			}

			this.ensureIndex(ensureParams).catch(error => {
				this.logger.error(`Failed to resume indexing for ${params.repoUri.fsPath}: ${error}`);
			});

			this.logger.info(`Restarting indexing process for repo: ${params.repoUri.fsPath}`);
			return true;
		} catch (error) {
			this.logger.error(`Failed to resume remote index for ${params.repoUri.fsPath} repo: ${error}`);
			return false;
		}
	}

	public async updateRemoteIndex(params: MainUpdateIndexParams): Promise<boolean> {
		if (this.allState[params.repoUri.fsPath].status !== 'completed') {
			this.logger.info(`the ${params.repoUri.fsPath} repo is not completed, skip update remote index`);
			return false;
		}

		const repoId = this.allState[params.repoUri.fsPath].repoId;
		const fileNode: IFileNode = {
			name: params.fileUri.fsPath.split('/').pop() || '',
			uri: params.fileUri,
			type: 'file',
			language: filenameToVscodeLanguage(params.fileUri.fsPath)
		}

		const relativePath = path.relative(params.repoUri.fsPath, params.fileUri.fsPath);
		if (shouldExclude(fileNode.name, defaultExcludeFiles) || shouldExcludePath(relativePath, defaultExcludeFolders)) {
			this.logger.info(`the ${params.fileUri.fsPath} file is excluded, skip update remote index`);
			return true;
		}

		try {
			switch (params.status) {
				case 'add':
					await this.processFileIndex(fileNode, repoId, 'create', params.repoUri);
					break;
				case 'delete':
					const relativePath = path.relative(params.repoUri.fsPath, params.fileUri.fsPath);
					await this.deleteFileIndex(relativePath, repoId, params.fileUri.scheme === 'file');
					break;
				case 'update':
					await this.processFileIndex(fileNode, repoId, 'update', params.repoUri);
					break;
				default:
					this.logger.error(`Invalid status: ${params.status}`);
					return false;
			}

			const repoSnapshotData = await this.getRepoSnapshot(repoId);
			this.logger.info(`repoSnapshotData: ${repoSnapshotData?.repoSnapshot.size}`);
			params.onProgress({
				repoUri: params.repoUri,
				progress: 1,
				SyncedFileNumber: repoSnapshotData?.repoSnapshot.size || 0,
			})

			this.logger.info(`Updated remote index for ${params.fileUri.fsPath} repo, status: ${params.status}`);
			return true;
		} catch (error) {
			this.logger.error(`Failed to update remote index for ${params.fileUri.fsPath} repo: ${error?.message}`);
			return false;
		}
	}

	private getIndexStatus(relativePath: string, hashcode: string, repoSnapshotData: RepoSnapshotData): 'unindexed' | 'indexed' | 'updating' {
		const hashcodeDst = repoSnapshotData.repoSnapshot.get(relativePath);
		if (hashcodeDst === undefined) {
			return 'unindexed';
		}

		if (hashcodeDst === hashcode) {
			return 'indexed';
		}

		return 'updating';
	}

	async checkCodebaseHealth(): Promise<boolean> {
		return (await this.codebaseRunner?.checkHealth())?.isHealthy ?? false;
	}

	private isDebugMode(): boolean {
		return this.productService.dataFolderName === '.flow-dev';
	}

	async startCodebaseProcess(): Promise<boolean> {
		if (this.codebaseRunner) {
			return await this.codebaseRunner.start();
		}

		this.logger.error('Codebase process is not initialized');
		return false;
	}

	async stopCodebaseProcess(): Promise<void> {
		if (this.codebaseRunner) {
			return await this.codebaseRunner.stop();
		}

		this.logger.warn('Codebase process is not initialized');
	}

	private async getCodebasePath(): Promise<string | null> {
		const dataFolderName = this.productService.dataFolderName;
		let codebasefile;
		if (osType === 'windows') {
			codebasefile = path.join(process.execPath, '..', 'tools', 'codebase', 'codebase.exe');
		} else {
			codebasefile = path.join(os.homedir(), dataFolderName, 'codebase', 'bin', 'codebase');
		}

		if (await this.fileService.exists(URI.file(codebasefile))) {
			return codebasefile;
		}

		this.logger.error(`codebasefile not found: ${codebasefile}`);
		return null;
	}

	// Repo状态管理
	private onRepoIndexRunning(path: string, repoId?: string, repoName?: string, repoPath?: string) {
		this.allState[path] = {
			...this.allState[path],
			repoId: repoId ?? this.allState[path]?.repoId,
			repoName: repoName ?? this.allState[path]?.repoName,
			repoPath: repoPath ?? this.allState[path]?.repoPath,
			status: 'running',
		};
	}

	private onRepoIndexCompleted(path: string, repoId?: string, repoName?: string, repoPath?: string) {
		this.allState[path] = {
			...this.allState[path],
			repoId: repoId ?? this.allState[path]?.repoId,
			repoName: repoName ?? this.allState[path]?.repoName,
			repoPath: repoPath ?? this.allState[path]?.repoPath,
			status: 'completed',
		};
	}

	private onRepoIndexPaused(path: string) {
		this.allState[path].status = 'paused';
	}

	private isRepoIndexCompleted(path: string): boolean {
		return this.allState[path]?.status === 'completed';
	}

	private isRepoIndexRunning(path: string): boolean {
		return this.allState[path]?.status === 'running';
	}

	private isRepoIndexPaused(path: string): boolean {
		if (!(path in this.allState)) {
			return true;
		}
		return this.allState[path].status === 'paused';
	}

	public async queryDependency(params: QueryDependencyParams): Promise<QueryDependencyResult[]> {
		try {
			if (!(params.repoUri.fsPath in this.allState)) {
				this.logger.info(`the ${params.repoUri.fsPath} repo is not exist, skip query dependency`);
				return [];
			}
			const repoId = await this.getRepoId(params.repoUri);
			if (!repoId) {
				this.logger.error(`repo not found, skip query dependency`);
				return [];
			}
			return await this.codebaseRunner?.post(CodebaseUrl.queryDependency, params);
		} catch (error) {
			this.logger.error(`Failed to query dependency for ${params.repoUri.fsPath} repo: ${error}`);
			return [];
		}
	}

	public async queryCallChain(params: QueryCallChainParams): Promise<QueryCallChainResult | undefined> {
		try {
			if (!(params.repoUri.fsPath in this.allState)) {
				this.logger.info(`the ${params.repoUri.fsPath} repo is not exist, skip query call chain`);
				return undefined;
			}
			const repoId = await this.getRepoId(params.repoUri);
			if (!repoId) {
				this.logger.error(`repo not found, skip query call chain`);
				return undefined;
			}
			return await this.codebaseRunner?.post(CodebaseUrl.queryCallChain, params);
		} catch (error) {
			this.logger.error(`Failed to query call chain for ${params.repoUri.fsPath} repo: ${error}`);
			return undefined;
		}
	}
}

registerSingleton(ICodebaseRemoteMainService, CodebaseRemoteMainService, InstantiationType.Eager);

