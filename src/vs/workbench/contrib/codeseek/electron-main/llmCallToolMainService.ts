import { createDecorator, IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { RipGrepSearchResult, SearchParamsType, RipGrepResult, TIMEOUT_MESSAGE, TIMEOUT_MS } from '../common/tools/toolTypes.js';
import { spawn } from 'child_process';
import { URI } from '../../../../base/common/uri.js';
import { IProductService } from '../../../../platform/product/common/productService.js';
import { osType } from '../browser/helpers/systemInfo.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import * as path from '../../../../base/common/path.js';
import os from 'os';

// 添加SearchOptions接口定义
interface SearchOptions {
	show_filename?: boolean;
	show_line_numbers?: boolean;
	count_matches?: boolean;
	files_with_matches?: boolean;
}

// 添加MatchResult接口定义
interface MatchResult {
	filePath: string;
	startLineNumber: number;
	endLineNumber: number;
	lineContent: string;
}

export interface IToolMainService {
	readonly _serviceBrand: undefined;

	ripGrepSearch(params: SearchParamsType): Promise<RipGrepResult>;
}

export const IToolMainService = createDecorator<IToolMainService>('toolMainService');
export class ToolMainService extends Disposable implements IToolMainService {
	readonly _serviceBrand: undefined;
	private ripGrepTool?: RipGrepToolMainService;

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IInstantiationService private readonly instantiationService: IInstantiationService,
	) {
		super();
	}

	async ripGrepSearch(params: SearchParamsType): Promise<RipGrepResult> {
		this.logger.info(`[ToolMainService] ripGrepSearch: ${JSON.stringify(params)}`);
		if (!this.ripGrepTool) {
			this.ripGrepTool = this.instantiationService.createInstance(RipGrepToolMainService);
		}
		return await this.ripGrepTool.search(params);
	}
}

registerSingleton(IToolMainService, ToolMainService, InstantiationType.Eager);

class RipGrepToolMainService {

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IProductService private readonly productService: IProductService,
		@IFileService private readonly fileService: IFileService,
	) {
	}

	async search(params: SearchParamsType): Promise<RipGrepResult> {
		try {
			this.logger.info(`[RipGrepToolMainService] searc params: ${JSON.stringify(params)}`);
			const cmd = await this._buildCommand(params.pattern, params.searchPaths, params.options);
			if (cmd.length === 0) {
				return { results: [] };
			}
			this.logger.info(`[RipGrepToolMainService] the rip grep cmd: ${cmd.join(' ')}`);
			const result = await this._executeCommand(cmd, TIMEOUT_MS);
			this.logger.info(`[RipGrepToolMainService] result: ${JSON.stringify(result)}`);

			// 解析输出并转换为RipGrepSearchResult[]
			const searchOptions: SearchOptions = {
				show_filename: true,
				show_line_numbers: true,
				count_matches: !!params.options.countMatches,
				files_with_matches: !!params.options.filesWithMatches
			};

			const matches = this._parseOutput(result.stdout, searchOptions);
			this.logger.info(`[RipGrepToolMainService] matches: ${JSON.stringify(matches)}`);
			let ripGrepResults: RipGrepSearchResult[] = matches.map(match => ({
				filePath: match.filePath,
				range: {
					startLineNumber: match.startLineNumber,
					startColumn: 1,
					endLineNumber: match.endLineNumber,
					endColumn: match.lineContent.length + 1
				},
				content: match.lineContent
			}));
			ripGrepResults = ripGrepResults.slice(0, params.options.maxResults ?? 50);

			return { results: ripGrepResults };
		} catch (error) {
			this.logger.error(`[RipGrepToolMainService] error: ${error}`);
			return { results: [] };
		}
	}

	private async _executeCommand(cmd: string[], timeoutMs: number): Promise<{ stdout: string, stderr: string, code: number | null }> {
		return new Promise((resolve, reject) => {
			try {
				const command = cmd[0];
				const args = cmd.slice(1);

				let stdout = '';
				let stderr = '';

				const childProcess = spawn(command, args, {
					shell: true
				});

				const timeoutId = setTimeout(() => {
					childProcess.kill();
					reject(new Error(TIMEOUT_MESSAGE));
				}, timeoutMs);

				childProcess.stdout?.on('data', (data: Buffer) => {
					stdout += data.toString('utf8');
				});

				childProcess.stderr?.on('data', (data: Buffer) => {
					stderr += data.toString('utf8');
				});

				childProcess.on('close', (code: number | null) => {
					clearTimeout(timeoutId);

					if (stderr && stderr.trim().length > 0 && code !== 0) {
						reject(new Error(`Ripgrep执行错误: ${stderr}`));
					} else {
						resolve({ stdout, stderr, code });
					}
				});

				childProcess.on('error', (error: Error) => {
					clearTimeout(timeoutId);

					if (error.message.includes('ENOENT')) {
						reject(new Error("ripgrep命令未找到"));
					} else {
						reject(error);
					}
				});

			} catch (error: any) {
				reject(new Error(`执行ripgrep命令时发生未知错误: ${error.message}`));
			}
		});
	}

	private async getRipGrepPath(): Promise<string | null> {
		const dataFolderName = this.productService.dataFolderName;
		let codebasefile;
		if (osType === 'windows') {
			codebasefile = path.join(process.execPath, '..', 'tools', 'ripgrep', 'rg.exe');
		} else {
			codebasefile = path.join(os.homedir(), dataFolderName, 'ripgrep', 'bin', 'rg');
		}

		if (await this.fileService.exists(URI.file(codebasefile))) {
			return codebasefile;
		}

		this.logger.error(`ripgrep not found: ${codebasefile}`);
		return null;
	}

	private async _buildCommand(
		pattern: string,
		searchPaths: string[],
		options: any
	): Promise<string[]> {
		const ripGrepPath = await this.getRipGrepPath();
		if (!ripGrepPath) {
			return [];
		}
		const cmd: string[] = [ripGrepPath];

		// 基础搜索模式
		if (!options.isRegex) {
			cmd.push('--fixed-strings');
		}

		// 大小写敏感性
		if (options.caseSensitivity === 'SENSITIVE') {
			cmd.push('--case-sensitive');
		} else if (options.caseSensitivity === 'INSENSITIVE') {
			cmd.push('--ignore-case');
		} else { // SMART
			cmd.push('--smart-case');
		}

		if (options.wholeWord) {
			cmd.push('--word-regexp');
		}

		if (options.multiline) {
			cmd.push('--multiline');
		}

		if (options.invertMatch) {
			cmd.push('--invert-match');
		}

		if (options.onlyMatching) {
			cmd.push('--only-matching');
		} else if (options.countMatches) {
			cmd.push('--count-matches');
		} else if (options.filesWithMatches) {
			cmd.push('--files-with-matches');
		}

		// 行号和文件名显示
		this._addOutputOptions(cmd, options);

		// 上下文行数
		if (options.contextBefore > 0) {
			cmd.push('--before-context', options.contextBefore.toString());
		}
		if (options.contextAfter > 0) {
			cmd.push('--after-context', options.contextAfter.toString());
		}

		// 文件过滤选项
		this._addFileFilterOptions(cmd, options);

		// 搜索范围控制
		this._addSearchScopeOptions(cmd, options);

		// 性能优化选项
		this._addPerformanceOptions(cmd, options);

		// 输出格式控制
		cmd.push('--color=never', '--no-heading');

		// 添加搜索模式和路径
		cmd.push('-e', pattern);
		cmd.push(...searchPaths);

		return cmd;
	}

	private _addOutputOptions(cmd: string[], options: any): void {
		if (options.showLineNumbers &&
			!options.countMatches &&
			!options.filesWithMatches) {
			cmd.push('--line-number');
		} else {
			cmd.push('--no-line-number');
		}

		if (options.showFilename &&
			!options.countMatches &&
			!options.filesWithMatches) {
			cmd.push('--with-filename');
		} else {
			cmd.push('--no-filename');
		}
	}

	private _addFileFilterOptions(cmd: string[], options: any): void {
		// 文件类型
		if (options.fileTypes) {
			for (const fileType of options.fileTypes) {
				cmd.push('--type', fileType);
			}
		}

		if (options.excludeFileTypes) {
			for (const excludeType of options.excludeFileTypes) {
				cmd.push('--type-not', excludeType);
			}
		}

		// Glob模式
		if (options.includeGlobs) {
			for (const glob of options.includeGlobs) {
				cmd.push('--glob', glob);
			}
		}

		if (options.excludeGlobs) {
			for (const glob of options.excludeGlobs) {
				cmd.push('--glob', `!${glob}`);
			}
		}
	}

	private _addSearchScopeOptions(cmd: string[], options: any): void {
		if (options.maxDepth !== undefined && options.maxDepth !== null) {
			cmd.push('--max-depth', options.maxDepth.toString());
		}

		if (options.followSymlinks) {
			cmd.push('--follow');
		}

		if (options.hiddenFiles) {
			cmd.push('--hidden');
		}

		if (options.respectGitignore === false) {
			cmd.push('--no-ignore');
		}
	}

	private _addPerformanceOptions(cmd: string[], options: any): void {
		// 二进制文件处理
		if (options.binaryHandling === 'TEXT') {
			cmd.push('--text');
		} else if (options.binaryHandling === 'BINARY') {
			cmd.push('--binary');
		}

		// 编码
		if (options.encoding && options.encoding !== 'auto') {
			cmd.push('--encoding', options.encoding);
		}

		// 文件大小限制
		if (options.maxFileSize) {
			cmd.push('--max-filesize', options.maxFileSize);
		}
	}

	private _parseOutput(output: string, options: SearchOptions): MatchResult[] {
		if (!output.trim()) {
			return [];
		}

		const lines = output.trim().split('\n');

		// 处理特殊输出模式
		if (options.count_matches || options.files_with_matches) {
			return this._parseSpecialOutput(lines, options);
		}

		// 解析标准输出
		return this._parseStandardOutput(lines, options);
	}

	private _parseSpecialOutput(lines: string[], options: SearchOptions): MatchResult[] {
		const matches: MatchResult[] = [];

		for (const line of lines) {
			if (!line.trim()) {
				continue;
			}

			if (options.files_with_matches) {
				matches.push({
					filePath: line.trim(),
					startLineNumber: 0,
					endLineNumber: 0,
					lineContent: ""
				});
			} else if (options.count_matches) {
				const parts = line.split(':', 1);
				if (parts.length === 2) {
					const file_path = parts[0];
					try {
						const count = parseInt(parts[1]);
						matches.push({
							filePath: file_path,
							startLineNumber: 0,
							endLineNumber: 0,
							lineContent: `匹配数量: ${count}`
						});
					} catch (e) {
						continue;
					}
				}
			}
		}

		return matches;
	}

	private _parseStandardOutput(lines: string[], options: SearchOptions): MatchResult[] {
		const matches: MatchResult[] = [];
		let currentMatch: MatchResult | null = null;

		for (const line of lines) {
			if (!line || line === '--') {
				continue;
			}

			try {
				// 检查是否是匹配行（带有":"的行）
				if (line.includes(':') && this._isMatchLine(line, options)) {
					const matchResult = this._parseMatchLine(line, options);
					if (matchResult) {
						if (currentMatch) {
							if (currentMatch.filePath === matchResult.file) {
								if (currentMatch.endLineNumber + 1 === matchResult.lineNumber) {
									currentMatch.endLineNumber = matchResult.lineNumber;
									currentMatch.lineContent += '\n' + matchResult.lineContent;
									continue;
								} else {
									matches.push(currentMatch);
								}
							} else {
								matches.push(currentMatch);
							}
						}
						currentMatch = {
							filePath: matchResult.file,
							startLineNumber: matchResult.lineNumber,
							endLineNumber: matchResult.lineNumber,
							lineContent: matchResult.lineContent
						};
					}
				}
				// 检查是否是上下文行（带有"-"的行）
				else if (line.includes('-') && this._isContextLine(line, options)) {
					const matchResult = this._parseContextLine(line, options);
					if (matchResult) {
						if (currentMatch) {
							if (currentMatch.filePath === matchResult.file) {
								if (currentMatch.endLineNumber + 1 === matchResult.lineNumber) {
									currentMatch.endLineNumber = matchResult.lineNumber;
									currentMatch.lineContent += '\n' + matchResult.lineContent;
									continue;
								} else {
									matches.push(currentMatch);
								}
							} else {
								matches.push(currentMatch);
							}
						}
						currentMatch = {
							filePath: matchResult.file,
							startLineNumber: matchResult.lineNumber,
							endLineNumber: matchResult.lineNumber,
							lineContent: matchResult.lineContent
						};
					}
				}
			} catch (e) {
				this.logger.error(`解析行时出错: ${e}`);
			}
		}

		if (currentMatch) {
			matches.push(currentMatch);
		}
		return matches;
	}

	private _isMatchLine(line: string, options: SearchOptions): boolean {
		if (!line) {
			return false;
		}

		// 匹配行格式: filename:line_number:content 或 line_number:content
		if (options.show_filename && options.show_line_numbers) {
			const regex = /^(.*):(\d+):(.*)[\r]?$/;
			const match = line.match(regex);
			if (match) {
				try {
					return parseInt(match[2]) > 0;
				} catch (e) {
					return false;
				}
			}
		} else if (options.show_filename) {
			return line.includes(':');
		} else if (options.show_line_numbers) {
			const regex = /^(\d+):(.*)[\r]?$/;
			const match = line.match(regex);
			if (match) {
				try {
					return parseInt(match[1]) > 0;
				} catch (e) {
					return false;
				}
			}
		} else {
			return true;
		}

		return false;
	}

	private _isContextLine(line: string, options: SearchOptions): boolean {
		if (!line || line === '--') {
			return false;
		}

		// 上下文行格式: filename-line_number-content 或 line_number-content
		if (options.show_filename && options.show_line_numbers) {
			const regex = /^(.*)-(\d+)-(.*)[\r]?$/;
			const match = line.match(regex);
			if (match) {
				try {
					return parseInt(match[2]) > 0;
				} catch (e) {
					return false;
				}
			}
		} else if (options.show_filename) {
			return line.includes('-');
		} else if (options.show_line_numbers) {
			// 格式: line_number-content
			const parts = line.split('-');
			if (parts.length >= 2) {
				return parseInt(parts[0]) > 0;
			}
		}

		return false;
	}

	private _parseContextLine(line: string, options: SearchOptions): { file: string, lineContent: string, lineNumber: number } | null {
		if (!line) {
			return null;
		}
		let filePath = "";
		let lineNumber = 1;
		let content = "";

		if (options.show_filename && options.show_line_numbers) {
			// 格式: filename-line_number-content
			const regex = /^(.*)-(\d+)-(.*)[\r]?$/;
			const match = line.match(regex);
			if (match) {
				filePath = match[1];
				lineNumber = parseInt(match[2]);
				content = match[3];
			}
		} else {
			throw new Error('Invalid line format');
		}

		return {
			file: filePath,
			lineContent: content,
			lineNumber: lineNumber
		};
	}

	private _parseMatchLine(line: string, options: SearchOptions): { file: string, lineContent: string, lineNumber: number } | null {
		if (!line) {
			return null;
		}
		let filePath = "";
		let lineNumber = 1;
		let content = "";

		if (options.show_filename && options.show_line_numbers) {
			// 格式: filename:line_number:content
			const regex = /^(.*?):(\d+):(.*)[\r]?$/;
			const match = line.match(regex);
			if (match) {
				try {
					filePath = match[1];
					lineNumber = parseInt(match[2]);
					content = match[3];
				} catch (e) {
					return null;
				}
			} else {
				return null;
			}
		} else {
			throw new Error('Invalid line format');
		}

		return {
			file: filePath,
			lineContent: content,
			lineNumber: lineNumber
		};
	}
}
