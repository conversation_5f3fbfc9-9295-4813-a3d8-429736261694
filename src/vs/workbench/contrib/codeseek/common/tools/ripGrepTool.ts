import { ICodeseekLogger } from '../codeseekLogService.js';
import { BaseTool, IBaseTool, TimeoutError } from './bsaeTool.js';
import { ToolCallParamsType, ToolNameEnum } from '../toolsServiceTypes.js';
import { IMainProcessService } from '../../../../../platform/ipc/common/mainProcessService.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { URI } from '../../../../../base/common/uri.js';
import { getRelativePath, getWorkspaceUri } from '../helpers/path.js';
import { RipGrepResult, RipGrepSearchResult, SearchParamsType, TIMEOUT_MESSAGE, TIMEOUT_MS } from './toolTypes.js';
import { IChannel } from '../../../../../base/parts/ipc/common/ipc.js';
import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../../platform/instantiation/common/extensions.js';


export type RipGrepToolResult = {
	status: 'success' | 'timeout' | 'error';
	message: string;
	result: RipGrepSearchResult[];
};

export interface IRipGrepTool extends IBaseTool {
	execute(args: ToolCallParamsType[ToolNameEnum.GREP_SEARCH]): Promise<RipGrepToolResult>;
}

export const IRipGrepTool = createDecorator<IRipGrepTool>('ripGrepTool');
export class RipGrepTool extends BaseTool implements IRipGrepTool {

	private channel: IChannel;
	private workspaceUri: URI | null = null;

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IMainProcessService private readonly mainProcessService: IMainProcessService,
		@IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService,
	) {
		super(logger);
		this.channel = this.mainProcessService.getChannel('codeseek-channel-tool');
		this.workspaceUri = getWorkspaceUri(this.workspaceContextService).workspaceUri;
	}

	override async execute(args: ToolCallParamsType[ToolNameEnum.GREP_SEARCH]): Promise<RipGrepToolResult> {
		this.logger.info('ripGrep tool args:', args);
		const { query, case_sensitive, exclude_pattern, include_pattern, explanation } = args;
		if (query.length === 0) {
			return {
				status: 'error',
				message: 'query is empty',
				result: [],
			};
		}
		const searchParams: SearchParamsType = {
			pattern: query,
			searchPaths: [this.workspaceUri?.fsPath ?? './'],
			options: {
				caseSensitivity: case_sensitive ? 'SENSITIVE' : 'INSENSITIVE',
				excludeGlobs: exclude_pattern ? exclude_pattern.split(',') : [],
				includeGlobs: include_pattern ? include_pattern.split(',') : [],
				contextBefore: 3,
				contextAfter: 3,
				maxResults: 50,
				showFilename: true,
				showLineNumbers: true,
				hiddenFiles: false,
				countMatches: false,
				filesWithMatches: false,
				onlyMatching: false,
			},
			explanation: explanation,
		};
		try {
			const timeoutPromise = new Promise<RipGrepResult>((_, reject) => {
				setTimeout(() => {
					reject(new TimeoutError(`${TIMEOUT_MESSAGE} (${ToolNameEnum.GREP_SEARCH})`));
				}, TIMEOUT_MS);
			});
			const executePromise = this.search(searchParams);
			const grepResult = await Promise.race([executePromise, timeoutPromise]);
			return {
				status: 'success',
				message: '',
				result: grepResult.results.map(r => ({
					filePath: getRelativePath(this.workspaceContextService, r.filePath),
					range: r.range,
					content: r.content,
				})),
			};
		} catch (error) {
			this.logger.error(`ripGrep search failed: ${error}`);
			if (error instanceof TimeoutError) {
				return {
					status: 'timeout',
					message: error.message,
					result: [],
				};
			}
			return {
				status: 'error',
				message: error.message,
				result: [],
			};
		}
	}

	private async search(searchParams: SearchParamsType): Promise<RipGrepResult> {
		const result = await this.channel.call('ripGrepSearch', searchParams);
		return result as RipGrepResult;
	}

}

registerSingleton(IRipGrepTool, RipGrepTool, InstantiationType.Eager);
