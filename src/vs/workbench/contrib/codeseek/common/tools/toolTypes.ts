
export const TIMEOUT_MS = 10000;
export const TIMEOUT_MESSAGE = 'Tool call timed out after 10s';

export type RipGrepSearchResult = {
	filePath: string,
	range: {
		startLineNumber: number;
		startColumn: number;
		endLineNumber: number;
		endColumn: number
	},
	content: string,
}

export type RipGrepResult = {
	results: RipGrepSearchResult[]
}

export type SearchParamsType = {
	pattern: string,
	searchPaths: string[],
	options: {
		showFilename?: boolean,
		showLineNumbers?: boolean,
		isRegex?: boolean,
		caseSensitivity?: 'SMART' | 'SENSITIVE' | 'INSENSITIVE',
		wholeWord?: boolean,
		fileTypes?: string[],
		includeGlobs?: string[],
		excludeGlobs?: string[],
		contextBefore?: number,
		contextAfter?: number,
		maxResults?: number,
		hiddenFiles?: boolean,
		countMatches?: boolean,
		filesWithMatches?: boolean,
		onlyMatching?: boolean,
	},
	explanation: string,
}
