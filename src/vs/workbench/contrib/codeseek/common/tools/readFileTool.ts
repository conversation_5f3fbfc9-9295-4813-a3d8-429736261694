import { URI } from '../../../../../base/common/uri.js';
import { IModelService } from '../../../../../editor/common/services/model.js';
import { InstantiationType, registerSingleton } from '../../../../../platform/instantiation/common/extensions.js';
import { createDecorator } from '../../../../../platform/instantiation/common/instantiation.js';
import { ICodeseekFileService } from '../codeseekFileService.js';
import { ICodeseekLogger } from '../codeseekLogService.js';
import { ToolNameEnum } from '../toolsServiceTypes.js';
import { BaseTool, TimeoutError } from './bsaeTool.js';
import { TIMEOUT_MESSAGE, TIMEOUT_MS } from './toolTypes.js';

export interface ReadFileToolParams {
	uri: URI;
	shouldReadEntireFile: boolean;
	startLineOneIndexed: number;
	endLineOneIndexed: number;
}

export interface ReadFileToolResult {
	status: 'success' | 'timeout' | 'error';
	message: string;
	result: {
		uri: URI;
		content: string;
		startLine: number;
		endLine: number;
	};
}

export interface IReadFileTool {
	readonly _serviceBrand: undefined;
	execute(params: ReadFileToolParams): Promise<ReadFileToolResult>;
}

export const IReadFileTool = createDecorator<IReadFileTool>('readFileTool');
export class ReadFileTool extends BaseTool implements IReadFileTool {
	readonly _serviceBrand: undefined;

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@ICodeseekFileService private readonly codeseekFileService: ICodeseekFileService,
		@IModelService private readonly modelService: IModelService,
	) {
		super(logger);
	}

	override async execute(params: ReadFileToolParams): Promise<ReadFileToolResult> {
		this.logger.info('readFileTool execute', params);
		let { uri, shouldReadEntireFile, startLineOneIndexed, endLineOneIndexed } = params;
		try {
			const timeoutPromise = new Promise<string>((_, reject) => {
				setTimeout(() => {
					reject(new TimeoutError(`${TIMEOUT_MESSAGE} (${ToolNameEnum.GREP_SEARCH})`));
				}, TIMEOUT_MS);
			});
			let executePromise;
			if (shouldReadEntireFile) {
				executePromise = this.codeseekFileService.readFile(uri);
				startLineOneIndexed = 0;
				endLineOneIndexed = 0;
			} else {
				executePromise = this.codeseekFileService.readFile(uri, {
					startLineNumber: startLineOneIndexed,
					endLineNumber: endLineOneIndexed,
				});
			}
			const content = await Promise.race([executePromise, timeoutPromise]);
			if (shouldReadEntireFile && content) {
				startLineOneIndexed = 1;
				const model = this.modelService.getModel(uri);
				if (model) {
					endLineOneIndexed = model.getLineCount();
				} else {
					endLineOneIndexed = (content.match(/\n/g) || []).length;
				}
			} else {
				if (content) {
					endLineOneIndexed = startLineOneIndexed + content.split('\n').length - 1;
				} else {
					startLineOneIndexed = 0;
					endLineOneIndexed = 0;
				}
			}
			return {
				status: 'success', message: '', result: {
					uri, content, startLine: startLineOneIndexed, endLine: endLineOneIndexed
				}
			};
		} catch (error) {
			this.logger.error('readFileTool error', error);
			if (error instanceof TimeoutError) {
				return { status: 'timeout', message: error.message, result: { uri, content: '', startLine: 0, endLine: 0 } };
			}
			return { status: 'error', message: 'Read file failed', result: { uri, content: '', startLine: 0, endLine: 0 } };
		}
	}
}

registerSingleton(IReadFileTool, ReadFileTool, InstantiationType.Eager);
