import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { Disposable, IDisposable } from '../../../../base/common/lifecycle.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { EnsureIndexParams, SearchParams, ICodebaseService, ISearchResult, QueryContextData, DeleteIndexParams, EventCodebaseOnProgressParams, ClangdConfigData, LLmConfigData, CreateRepoConfig, PauseRemoteIndexParams, ResumeRemoteIndexParams, CodeContextItem, FileReadRequest, FileReadSizeRequest, SearchReqParams } from './codebaseTypes.js';
import { ICodeseekSettingsService } from './codeseekSettingsService.js';
import { CodebaseModes, FeatureNames, SettingsOfCodebase } from './codeseekSettingsTypes.js';
import { getWorkspaceUri } from './helpers/path.js';
import { defaultExcludeFolders, ICodeseekFileService } from './codeseekFileService.js';
import { URI } from '../../../../base/common/uri.js';
import { ICodeseekLogger } from './codeseekLogService.js';
import { IChannel } from '../../../../base/parts/ipc/common/ipc.js';
import { Event } from '../../../../base/common/event.js';
import { IMainProcessService } from '../../../../platform/ipc/common/mainProcessService.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { IFileService, FileChangeType } from '../../../../platform/files/common/files.js';
import { autorun, derived } from '../../../../base/common/observable.js';
import { ISCMViewService } from '../../scm/common/scm.js';
import * as path from '../../../../base/common/path.js';
import { CODESEEK_VIEW_CONTAINER_ID } from '../browser/actionIDs.js';
import { LogLevel } from '../../../../platform/log/common/log.js';

export interface ICodebaseRemoteService extends ICodebaseService {
	readonly _serviceBrand: undefined;

	initialize(): void;
}

export const ICodebaseRemoteService = createDecorator<ICodebaseRemoteService>('codeseekRemoteService');
export class CodebaseRemoteService extends Disposable implements ICodebaseRemoteService {
	readonly _serviceBrand: undefined;
	private workspaceUri: URI | null = null;
	private readonly channel: IChannel;
	private fileWatcher: IDisposable | undefined;
	private pendingFileChanges: Map<string, { uri: URI, status: 'add' | 'delete' | 'update' }> = new Map();
	private updateInterval: any;
	private readonly periodicUpdateTime = 2 * 60 * 1000; // 2分钟
	private isRunningCodebaseProcess = false;
	private isFirstInit = true;
	private currentAuthority: string | undefined;

	private readonly codebaseHooks = {
		onProgress: {} as { [repoUriStr: string]: ((params: EventCodebaseOnProgressParams) => void) },
	};

	private readonly _activeRepositoryHistoryItemRefName = derived(reader => {
		const repository = this.scmViewService.activeRepository.read(reader);
		const historyProvider = repository?.provider.historyProvider.read(reader);
		const historyItemRef = historyProvider?.historyItemRef.read(reader);

		return historyItemRef?.name;
	});

	constructor(
		@IMainProcessService private readonly _mainProcessService: IMainProcessService,
		@IWorkspaceContextService private _workspaceContextService: IWorkspaceContextService,
		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
		@ICodeseekFileService private readonly _codeseekFileService: ICodeseekFileService,
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IConfigurationService private readonly _configurationService: IConfigurationService,
		@IFileService private readonly _fileService: IFileService,
		@ISCMViewService private readonly scmViewService: ISCMViewService,
	) {
		super();
		this.logger.setLevel(LogLevel.Info);
		this.channel = this._mainProcessService.getChannel('codeseek-channel-codebase');
		this.workspaceUri = getWorkspaceUri(this._workspaceContextService).workspaceUri;

		// 获取当前工作区的权限
		if (this.workspaceUri && this.workspaceUri.scheme === 'vscode-remote') {
			this.currentAuthority = this.workspaceUri.authority;
			this.logger.debug(`[CodebaseRemoteService] Detected remote authority: ${this.currentAuthority}`);
		} else {
			this.currentAuthority = undefined;
			this.logger.debug(`[CodebaseRemoteService] Local workspace detected`);
		}

		this.logger.debug('CodebaseRemoteService: Starting initialization...');

		this.startCodebaseProcess();
		this.setupFileWatcher();

		this._register({
			dispose: () => {
				this.stopCodebaseProcess()
				this.disposeFileWatcher();
				this.stopPeriodicUpdate();
			}
		});

		this._register(autorun(reader => {
			const repository = this.scmViewService.activeRepository.read(reader);
			const historyItemRefName = this._activeRepositoryHistoryItemRefName.read(reader);
			this.logger.info(`active repository: ${repository?.provider.name}, branch: ${historyItemRefName}`);
			const status = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote]?.status;
			if (this.isRunningCodebaseProcess && status === 'completed') {
				if (repository && historyItemRefName) {
					this.logger.info(`the repository checkout branch, to update codebase`);
					this.initialize();
				}
			}
		}));

		this._register(this._workspaceContextService.onDidChangeWorkspaceFolders(() => {
			this.startCodebaseProcess();
			this.setupFileWatcher();
		}));

		this._register(this._codeseekSettingsService.onDidDeleteCodebase(async e => {
			this.logger.info(`delete codebase of workspace: ${this.workspaceUri?.fsPath}`);
			this.deleteIndex({
				repoUri: this.workspaceUri!,
			});
		}));
		this._register(this._codeseekSettingsService.onDidReSyncCodebase(async e => {
			this.logger.info(`reSync codebase of workspace: ${this.workspaceUri?.fsPath}`);
			await this.deleteIndex({
				repoUri: this.workspaceUri!,
			}, false);
			this.initialize();
		}));

		this._register(this._codeseekSettingsService.onDidPauseCodebase(async e => {
			this.logger.info(`pause codebase of workspace: ${this.workspaceUri?.fsPath}`);
			await this.pauseRemoteIndex({
				repoUri: this.workspaceUri!,
			});
		}));

		this._register(this._codeseekSettingsService.onDidResumeCodebase(async e => {
			this.logger.info(`resume codebase of workspace: ${this.workspaceUri?.fsPath}`);
			await this.resumeRemoteIndex({
				repoUri: this.workspaceUri!,
			});
		}));

		this._register((this.channel.listen('onProgress_remote') satisfies Event<EventCodebaseOnProgressParams>)(e => {
			this.codebaseHooks.onProgress[e.repoUri.fsPath]?.(e);
		}));

		this._register(this._codeseekSettingsService.onDidUpdateCodebase(() => {
			this.startPeriodicUpdate();
		}));

		this._register(this._codeseekSettingsService.onDidChangeCodebaseSwitchStatus(() => {
			if (!this.workspaceUri) {
				return;
			}
			const isRemoteCodebaseEnabled = this._codeseekSettingsService.state.globalSettings.enableCodeBase;
			const repoSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri.fsPath];
			const remoteSettings = repoSettings?.[CodebaseModes.Remote];
			if (isRemoteCodebaseEnabled) {
				if (this.isRunningCodebaseProcess) {
					this.initialize();
				}
			} else {
				if (remoteSettings && remoteSettings.process && remoteSettings.process > 0 && remoteSettings.process < 1) {
					this.pauseRemoteIndex({
						repoUri: this.workspaceUri
					});
				}
			}
		}));

		// 延迟注册事件监听器，确保channel完全初始化
		setTimeout(() => {
			this.setupFileReadListener();
			this.setupFileReadSizeListener();
		}, 1000);
	}

	private setupFileReadSizeListener(): void {
		const fileReadSizeRequestEvent = this.channel.listen('fileReadSizeRequest') as Event<FileReadSizeRequest>;

		this._register(fileReadSizeRequestEvent(async (event: FileReadSizeRequest) => {
			// 权限检查：确保请求的URI权限与当前实例匹配
			if (event.authority && this.currentAuthority && event.authority !== this.currentAuthority) {
				this.logger.debug(`[Renderer] Received file read size request for different authority: ${event.authority}, expected: ${this.currentAuthority}, ignoring request ${event.requestId}`);
				return;
			}

			this.logger.debug(`[Renderer] Processing file read request: ${event.requestId}, Authority: ${event.authority || 'default'}`);
			try {
				const uri = URI.revive(event.uri);
				const size = await this.readFileSize(uri);
				this.logger.debug(`[Renderer] Successfully read file size: ${size}`);
				await this.channel.call('fileReadSizeResponse', {
					requestId: event.requestId,
					size: size
				});
			} catch (error) {
				this.logger.error(`[Renderer] Failed to read file size for request ${event.requestId}:`, error);
			}
		}));
	}

	private setupFileReadListener(): void {
		const fileReadRequestEvent = this.channel.listen('fileReadRequest') as Event<FileReadRequest>;

		this._register(fileReadRequestEvent(async (event: FileReadRequest) => {
			// 权限检查：确保请求的URI权限与当前实例匹配
			if (event.authority && this.currentAuthority && event.authority !== this.currentAuthority) {
				this.logger.debug(`[Renderer] Received file read request for different authority: ${event.authority}, expected: ${this.currentAuthority}, ignoring request ${event.requestId}`);
				return;
			}

			this.logger.debug(`[Renderer] Processing file read request: ${event.requestId}, Authority: ${event.authority || 'default'}`);

			try {
				const uri = URI.revive(event.uri);
				this.logger.debug(`[Renderer] Reading file: ${uri.toString()}`);

				const result = await this.readFileContent(uri);
				this.logger.debug(`[Renderer] Successfully read file, content length: ${result.length}`);

				// 发送响应回主进程
				await this.channel.call('fileReadResponse', {
					requestId: event.requestId,
					result: result
				});

				this.logger.debug(`[Renderer] Sent file read success response for request: ${event.requestId}`);
			} catch (error) {
				this.logger.error(`[Renderer] Failed to read file for request ${event.requestId}:`, error);

				// 发送错误回主进程
				await this.channel.call('fileReadResponse', {
					requestId: event.requestId,
					error: (error as Error).message
				});

				this.logger.debug(`[Renderer] Sent file read error response for request: ${event.requestId}`);
			}
		}));

		this.logger.info(`CodebaseRemoteService: File read request listener registered successfully for authority: ${this.currentAuthority || 'local'}`);
	}

	private setupFileWatcher(): void {
		this.disposeFileWatcher();

		if (!this.workspaceUri) {
			this.logger.info('workspaceUri is not set, skip setting up file watcher');
			return;
		}

		this.fileWatcher = this._fileService.watch(this.workspaceUri, {
			recursive: true,
			excludes: Array.from(defaultExcludeFolders),
		});

		this._register(this._fileService.onDidFilesChange(async e => {
			if (!this.workspaceUri || !this._codeseekSettingsService.state.globalSettings.enableCodeBase) {
				return;
			}

			if (e.affects(this.workspaceUri, FileChangeType.ADDED, FileChangeType.DELETED, FileChangeType.UPDATED)) {
				if (e.gotAdded()) {
					for (const resource of e.rawAdded) {
						this.pendingFileChanges.set(resource.fsPath, { uri: resource, status: 'add' });
					}
				}

				if (e.gotDeleted()) {
					for (const resource of e.rawDeleted) {
						this.pendingFileChanges.set(resource.fsPath, { uri: resource, status: 'delete' });
					}
				}

				if (e.gotUpdated()) {
					for (const resource of e.rawUpdated) {
						this.pendingFileChanges.set(resource.fsPath, { uri: resource, status: 'update' });
					}
				}
			}
		}));
	}

	private startPeriodicUpdate(): void {
		if (this.updateInterval) {
			return;
		}
		this.logger.info(`start periodic update for codebase of workspace: ${this.workspaceUri?.fsPath}`);
		this.updateInterval = setInterval(() => {
			if (this.pendingFileChanges.size > 0) {
				this.processFileChanges();
			}
		}, this.periodicUpdateTime);
	}

	private stopPeriodicUpdate(): void {
		if (this.updateInterval) {
			clearInterval(this.updateInterval);
			this.updateInterval = undefined;
		}
	}

	private async processFileChanges(): Promise<void> {
		if (this.pendingFileChanges.size === 0) {
			return;
		}

		const changeEntries = Array.from(this.pendingFileChanges.entries());
		const batchSize = 10; // 每批处理的文件数量
		const failedChanges: Map<string, { uri: URI, status: 'add' | 'delete' | 'update' }> = new Map();

		for (let i = 0; i < changeEntries.length; i += batchSize) {
			const batch = changeEntries.slice(i, i + batchSize);
			const results = await Promise.allSettled(batch.map(async ([filePath, change]) => {
				try {
					const isUpdateSuccess: boolean = await this.channel.call('updateRemoteIndex', {
						repoUri: this.workspaceUri!,
						fileUri: change.uri,
						status: change.status,
					});
					return { filePath, isUpdateSuccess };
				} catch (error) {
					this.logger.error(`索引更新异常: ${error instanceof Error ? error.message : String(error)}`);
					return { filePath, isUpdateSuccess: false };
				}
			}));

			for (const result of results) {
				if (result.status === 'fulfilled') {
					const { filePath, isUpdateSuccess } = result.value;
					if (isUpdateSuccess) {
						this.pendingFileChanges.delete(filePath);
					} else {
						failedChanges.set(filePath, this.pendingFileChanges.get(filePath)!);
					}
				}
			}
		}

		if (failedChanges.size > 0) {
			this.logger.info(`${failedChanges.size} 个文件变更处理失败，将在下次更新中重试`);
			for (const [filePath, change] of failedChanges.entries()) {
				this.pendingFileChanges.set(filePath, change);
			}
		}
	}

	private disposeFileWatcher(): void {
		if (this.fileWatcher) {
			this.fileWatcher.dispose();
			this.fileWatcher = undefined;
		}
	}

	public async startCodebaseProcess() {
		this.isRunningCodebaseProcess = await this.channel.call('startRemoteCodebase');
		if (this.isRunningCodebaseProcess) {
			this.initialize();
		}
	}

	public async stopCodebaseProcess() {
		this.channel.call('stopRemoteCodebase');
		this.isRunningCodebaseProcess = false;
	}

	public initialize() {
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (!this.isFirstInit) {
			if (currentSettings?.status === 'indexing') {
				this.logger.info('already initializing, skip duplicate call');
				return;
			}
		}
		if (!this._codeseekSettingsService.state.globalSettings.enableCodeBase) {
			this.logger.info('remote codebase is not enabled, skip initialize');
			return;
		}
		if (!this.workspaceUri) {
			this.logger.info('workspaceUri is not set, skip initialize');
			return;
		}
		if (!['file', 'vscode-remote'].includes(this.workspaceUri.scheme)) {
			this.logger.info('workspaceUri is not a file or vscode-remote, skip initialize');
			return;
		}
		this.isFirstInit = false;
		this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
			[CodebaseModes.Remote]: {
				...currentSettings,
				status: 'indexing',
			}
		} as SettingsOfCodebase);
		this.pendingFileChanges.clear();
		this.logger.info(`the codebase of repo: ${this.workspaceUri.fsPath} start initialize`);
		this.ensureIndex({
			repoUri: this.workspaceUri,
			options: {
				retryIfLastAttemptFailed: true,
				ignoreExisting: false
			},
			onProgress: (p) => {
				this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
					[CodebaseModes.Remote]: {
						process: p.progress,
						syncedFileNumber: p.SyncedFileNumber,
						status: p.progress === 1 ? 'completed' : 'indexing',
					}
				} as SettingsOfCodebase);
			},
		});

		// 对于远程连接，增加连接状态检查
		if (this.workspaceUri.scheme === 'vscode-remote') {
			this.logger.info('Detected remote workspace, checking connection status...');
			// 可以添加远程连接健康检查
			this.checkRemoteConnection().then(isHealthy => {
				if (!isHealthy) {
					this.logger.warn('Remote connection appears unstable, codebase indexing may be affected');
				}
			}).catch(error => {
				this.logger.error('Failed to check remote connection:', error);
			});
		}
	}

	private async checkRemoteConnection(): Promise<boolean> {
		try {
			// 尝试读取一个小文件来测试连接
			const testUri = this.workspaceUri!.with({ path: this.workspaceUri!.path + '/.vscode' });
			const exists = await this._fileService.exists(testUri);
			this.logger.debug(`Remote connection test result: ${exists}`);
			return true;
		} catch (error) {
			this.logger.error('Remote connection test failed:', error);
			return false;
		}
	}

	public async search(params: SearchParams): Promise<ISearchResult[]> {
		if (!this._codeseekSettingsService.state.globalSettings.enableCodeBase) {
			this.logger.info('remote codebase is not enabled, skip search');
			return [];
		}
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (currentSettings?.status !== 'completed') {
			this.logger.info('codebase is not completed, skip search');
			return [];
		}
		try {
			const queryResults: CodeContextItem[] | undefined = await this.getResults(params);
			if (!queryResults || queryResults.length === 0) {
				return [];
			}
			const searchResults: ISearchResult[] = queryResults.map((item) => {
				const fullFilePath = path.join(this.workspaceUri!.fsPath, item.relativePath);
				const uri = URI.file(fullFilePath);
				return {
					uri,
					content: item.content,
					range: item.range
				}
			});
			return searchResults;
		} catch (error) {
			this.logger.error(`search failed: ${error instanceof Error ? error.message : String(error)}`);
			return [];
		}
	}

	private async ensureIndex(params: EnsureIndexParams): Promise<void> {
		this.codebaseHooks.onProgress[params.repoUri.fsPath] = params.onProgress;
		try {
			const clangd: ClangdConfigData | undefined = this.getClangdConfig();
			const projectStructure = await this._codeseekFileService.getProjectStructure(Infinity);

			const params_: EnsureIndexParams & CreateRepoConfig = {
				...params,
				clangd,
				projectStructure
			};

			await this.channel.call('ensureRemoteIndex', params_);
		} catch (error) {
			this.logger.error(`索引创建失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	private async readFileSize(uri: URI): Promise<number> {
		try {
			// 尝试检查文件是否存在，这会触发文件系统提供者的检查
			try {
				await this._fileService.exists(uri);
			} catch (fsError) {
				this.logger.error(`File system provider check failed for ${uri.toString()}:`, fsError);
				throw new Error(`No file system provider found for resource '${uri.toString()}'`);
			}

			const fileStat = await this._fileService.stat(uri);
			return fileStat.size;
		} catch (error) {
			this.logger.error(`Failed to read file size in renderer process: ${uri.toString()}`, error);
			throw error;
		}
	}

	private async readFileContent(uri: URI): Promise<string> {
		try {
			this.logger.debug(`Reading file content in renderer process: ${uri.toString()}`);

			// 尝试检查文件是否存在，这会触发文件系统提供者的检查
			try {
				await this._fileService.exists(uri);
			} catch (fsError) {
				this.logger.error(`File system provider check failed for ${uri.toString()}:`, fsError);
				throw new Error(`No file system provider found for resource '${uri.toString()}'`);
			}

			const content = await this._fileService.readFile(uri);
			const contentStr = content.value.toString();
			this.logger.debug(`Successfully read file, content length: ${contentStr.length}`);
			return contentStr;
		} catch (error) {
			this.logger.error(`Failed to read file content in renderer process: ${uri.toString()}`, error);
			throw error;
		}
	}

	private async deleteIndex(params: DeleteIndexParams, isResetProcess: boolean = true): Promise<boolean> {
		this.stopPeriodicUpdate();
		this.pendingFileChanges.clear();
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (currentSettings?.status === 'idle') {
			return true;
		}
		const isDeleteSuccess: boolean = await this.channel.call('deleteRemoteIndex', params);
		if (isDeleteSuccess) {
			this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
				[CodebaseModes.Remote]: {
					process: isResetProcess ? undefined : 0,
					syncedFileNumber: 0,
					status: 'idle',
				}
			} as SettingsOfCodebase);
		}
		return isDeleteSuccess;
	}

	private async getResults(params: SearchParams): Promise<CodeContextItem[] | undefined> {
		const modelSelection = this._codeseekSettingsService.getModelSelectionForContainer(FeatureNames.CtrlL, CODESEEK_VIEW_CONTAINER_ID);
		if (!modelSelection) {
			return;
		}

		const { providerName, modelName } = modelSelection;
		const provider = this._codeseekSettingsService.state.settingsOfProvider[providerName];
		const baseUrl = (!provider.baseURL || provider.baseURL === '') ? provider.endpoint : provider.baseURL;
		const llm: LLmConfigData = {
			provider: providerName,
			model: modelName,
			baseUrl: baseUrl ?? '',
			apiKey: provider.apiKey,
		};

		const requestParams: SearchReqParams = {
			...params,
			llm,
		};

		const queryResults: QueryContextData | undefined = await this.channel.call('getRemoteResults', requestParams);
		this.logger.info(`getResults, queryResults: ${JSON.stringify(queryResults, null, 2)}`);
		if (queryResults) {
			const validItems: CodeContextItem[] = [];
			for (const item of queryResults.codeContextItems) {
				const fullFilePath = path.join(this.workspaceUri!.fsPath, item.relativePath);
				const fileUri = URI.file(fullFilePath);
				try {
					const exists = await this._fileService.exists(fileUri);
					if (!exists) {
						continue;
					}
					if (!item.content) {
						continue;
					}

					if (this.pendingFileChanges.has(fullFilePath)) {
						const change = this.pendingFileChanges.get(fullFilePath)!;
						if (change.status === 'update') {
							item.content = await this._codeseekFileService.readFile(fileUri, {
								startLineNumber: item.range.startLineNumber,
								endLineNumber: item.range.endLineNumber,
							});
						}
					}
					validItems.push(item);
				} catch (error) {
					this.logger.error(`处理查询结果时出错: ${error instanceof Error ? error.message : String(error)}`);
				}
			}
			return validItems;
		}
		return undefined;
	}

	private async pauseRemoteIndex(params: PauseRemoteIndexParams): Promise<boolean> {
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (currentSettings?.status === 'paused') {
			return true;
		}
		try {
			const isPauseSuccess: boolean = await this.channel.call('pauseRemoteIndex', params);
			this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
				[CodebaseModes.Remote]: {
					...currentSettings,
					status: 'paused',
				}
			} as SettingsOfCodebase);
			return isPauseSuccess;
		} catch (error) {
			this.logger.error(`索引暂停失败: ${error instanceof Error ? error.message : String(error)}`);
			return false;
		}
	}

	private async resumeRemoteIndex(params: ResumeRemoteIndexParams): Promise<boolean> {
		const currentSettings = this._codeseekSettingsService.state.settingsOfCodebase[this.workspaceUri!.fsPath]?.[CodebaseModes.Remote];
		if (currentSettings?.status === 'indexing') {
			return true;
		}
		try {
			const isResumeSuccess: boolean = await this.channel.call('resumeRemoteIndex', params);
			this._codeseekSettingsService.setSettingsOfCodebase(this.workspaceUri!, {
				[CodebaseModes.Remote]: {
					...currentSettings,
					status: 'indexing',
				}
			} as SettingsOfCodebase);
			return isResumeSuccess;
		} catch (error) {
			this.logger.error(`索引恢复失败: ${error instanceof Error ? error.message : String(error)}`);
			return false;
		}
	}

	private getClangdConfig(): ClangdConfigData | undefined {
		const clangdCPath = this._configurationService.getValue<string>('clangd.path');
		const clangdArguments: string[] = this._configurationService.getValue<string[]>('clangd.arguments');
		if (!clangdCPath || !clangdArguments) return undefined;
		const compileCommandsDir = clangdArguments.find(arg => arg.startsWith('--compile-commands-dir'));
		if (!compileCommandsDir) return undefined;
		const parts = compileCommandsDir.split('=');
		const compileCommandsDirPath = parts.length > 1 ? parts[1] : '';
		if (!compileCommandsDirPath) return undefined;
		return {
			path: clangdCPath,
			compileCommandsDir: compileCommandsDirPath,
		}
	}
}

registerSingleton(ICodebaseRemoteService, CodebaseRemoteService, InstantiationType.Delayed);
