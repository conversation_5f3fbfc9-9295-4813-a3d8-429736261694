import { CancellationToken } from '../../../../base/common/cancellation.js';
import { URI } from '../../../../base/common/uri.js';
import { ISearchResult } from './codebaseTypes.js';
import { Plan } from './remoteAgentServiceType.js';

export type ToolCallType = {
	name: ToolName | string;
	params: ToolCallParamsType[ToolName] | Plan;
	id: string;
};

export const enum ToolCallStatus {
	idle = "idle",
	executing = "executing",
	success = "success",
	failure = "failure",
}

export type ToolCallResultType = {
	status: ToolCallStatus;
	name: ToolName;
	content: string;
	toolCallReturn: ToolCallReturnType[ToolName] | null;
	error: string | undefined;
	topic?: string;
};

export enum AskReponseType {
	yesButtonClicked = 'yesButtonClicked',
	noButtonClicked = 'noButtonClicked',
	messageResponse = 'messageResponse',
}

export type AskResponse = {
	type: string;
	response?: AskReponseType;
	text?: string;
	selectedIndex?: number;
};

export type InternalToolInfo = {
	name: string;
	description: string;
	params: {
		[paramName: string]: { type: string; description: string | undefined }; // name -> type
	};
	required: string[]; // required paramNames
	needApprove: boolean;
	example: string;
};

// 定义工具名称枚举
export enum ToolNameEnum {
	READ_FILE = 'read_file',
	EDIT_FILE = 'edit_file',
	LIST_DIR = 'list_dir',
	FILE_SEARCH = 'file_search',
	DELETE_FILE = 'delete_file',
	CREATE_FILE = 'create_file',
	APPROVE_REQUEST = 'approve_request',
	ASK_FOLLOWUP_QUESTION = 'ask_followup_question',
	CTAGS_QUERY = 'ctags_query',
	CLANGD_QUERY = 'clangd_query',
	SHOW_SUMMARY = 'show_summary',
	SHOW_CONTENT = 'show_content',
	CODEBASE_SEARCH = 'codebase_search',
	EXEC_COMMAND = 'exec_command',
	GREP_SEARCH = 'grep_search',
	CALL_TRACE_QUERY = 'call_trace_query',
}

export const codeseekTools = {
	[ToolNameEnum.READ_FILE]: {
		name: ToolNameEnum.READ_FILE,
		description: `请求读取指定相对路径下的文件内容。当您需要查看一个未知内容的现有文件时使用此功能，例如分析代码、审阅文本文件或从配置文件中提取信息。此工具调用的输出将是文件从起始行（1开始计数）到结束行（包括结束行）的内容，以及起始行之前和结束行之后内容的摘要。\n注意：此工具每次最多只能读取250行。\n\n使用此工具收集信息时，您有责任确保获取完整的上下文。具体而言，每次调用此命令时，您应：\n1) 评估已查看的内容是否足以继续完成任务。\n2) 注意未显示的行。\n3) 如果已查看的内容不足，且怀疑关键信息可能在未显示的行中，应主动再次调用此工具查看这些行。\n4) 如有疑问，应再次调用此工具以获取更多信息。请注意，部分文件内容可能会遗漏关键的依赖项、导入或功能。\n\n在某些情况下，如果读取行范围不足，您可以选择读取整个文件。\n读取整个文件通常效率低下且耗时，尤其是对于大文件（即超过几百行的文件）。因此应谨慎使用此选项。\n大多数情况下不允许读取整个文件。只有在用户编辑或手动将文件附加到对话中时，才允许读取整个文件。`,
		params: {
			path: { type: 'string', description: "要读取的文件的相对路径（相对于当前工作目录）" },
			start_line_one_indexed: { type: 'number', description: "读取起始的行号（1开始计数，包括该行）" },
			end_line_one_indexed: { type: 'number', description: "读取结束的行号（1开始计数，包括该行）" },
			should_read_entire_file: { type: "boolean", description: "是否读取整个文件。默认为false" },
		},
		required: ['path', 'start_line_one_indexed', 'end_line_one_indexed', 'should_read_entire_file'],
		needApprove: false,
		example: `\`\`\`
<read_file>
<path>example_dir/example.py</path>
<start_line_one_indexed>1</start_line_one_indexed>
<end_line_one_indexed>250</end_line_one_indexed>
<should_read_entire_file>false</should_read_entire_file>
</read_file>
\`\`\``
	},
	[ToolNameEnum.EDIT_FILE]: {
		name: ToolNameEnum.EDIT_FILE,
		description: `请求将变更内容编辑到文件中。当用户请求编辑他们的代码或生成新的代码时，请输出一个简化版的代码块，突出显示需要进行的更改，并添加注释以标明跳过了哪些未更改的代码。注意：文件路径不存在时，工具会自动创建目录和文件。`,
		params: {
			path: { type: 'string', description: "要编辑的文件的相对路径（相对于当前工作目录）" },
			content: {
				type: 'string', description: `这些编辑代码块也会被一个智能程度较低的语言模型（俗称"应用模型"）读取，用于更新文件。为了帮助向应用模型明确指定编辑内容，你在生成代码块时需要格外小心，避免引入歧义。对于文件中所有未修改的区域（包括代码和注释），你都需要用"// ... existing code ..."这样的注释标记来标注。这样可以确保应用模型在编辑文件时不会删除现有的未修改代码或注释。注意不要提及应用模型。`
			},
		},
		required: ['path', 'content'],
		needApprove: false,
		example: `
\`\`\`
<edit_file>
<path>example_dir/example.py</path>
<content>
// ... existing code ...
int main() {
    int x = 0; // 初始化变量
    std::cout << x << std::endl;
    return 0;
}
</content>
</edit_file>
\`\`\`
`
	},
	[ToolNameEnum.LIST_DIR]: {
		name: ToolNameEnum.LIST_DIR,
		description: `列出目录的内容。在使用更有针对性的工具（如语义搜索或文件读取）之前，用于发现的快速工具。在深入研究特定文件之前尝试了解文件结构非常有用。可用于探索代码库。`,
		params: {
			relative_workspace_path: { type: 'string', description: "相对于工作空间根目录的列表内容路径" },
		},
		required: ['relative_workspace_path'],
		needApprove: false,
		example: ''
	},

	[ToolNameEnum.FILE_SEARCH]: {
		name: ToolNameEnum.FILE_SEARCH,
		description: `基于与文件路径的模糊匹配的快速文件搜索。如果您知道文件路径的一部分，但不知道它的确切位置，请使用它。响应将限制为 10 个结果。如果需要进一步筛选结果，请使您的查询更具体`,
		params: {
			query: { type: 'string', description: "要搜索的模糊文件名（如 '*.js' 或 'src/**/test_*.py'），支持 glob 语法或基础正则表达式。" },
		},
		required: ['query'],
		needApprove: false,
		example: ''
	},

	[ToolNameEnum.DELETE_FILE]: {
		name: ToolNameEnum.DELETE_FILE,
		description: `删除指定路径处的文件。如果出现以下情况，该作将正常失败：
- 文件不存在
- 出于安全原因，该作被拒绝
- 无法删除该文件`,
		params: {
			target_file: { type: 'string', description: "要删除的文件的路径，相对于工作区根目录。" },
		},
		required: ['target_file'],
		needApprove: true,
		example: ''
	},

	[ToolNameEnum.CREATE_FILE]: {
		name: ToolNameEnum.CREATE_FILE,
		description: `使用给定的文件内容在给定的文件路径创建一个新文件`,
		params: {
			path: { type: 'string', description: "给定的文件路径" },
			content: { type: 'string', description: "给定的文件内容" },
		},
		required: ['path', 'content'],
		needApprove: true,
		example: ''
	},

	[ToolNameEnum.APPROVE_REQUEST]: {
		name: ToolNameEnum.APPROVE_REQUEST,
		description: `发起一个合入请求`,
		params: {
			content: { type: 'string', description: '前一次的尝试结果' },
			command: { type: 'string', description: '演示结果的工具（可选）' },
		},
		required: ['content'],
		needApprove: false,
		example: ''
	},

	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: {
		name: ToolNameEnum.ASK_FOLLOWUP_QUESTION,
		description: `向用户提问以收集完成任务所需额外信息。当您遇到歧义、需要澄清或需要更多细节以有效进行时，应使用此工具。它通过允许与用户直接沟通，实现互动解决问题。按优先级或逻辑顺序排列建议答案，用户只能选择其中一个建议。谨慎使用此工具，以在收集必要信息和避免过多来回沟通之间保持平衡。该工具在**单次响应中只允许调用一次**。如需多次提问，请分步骤顺序迭代调用。`,
		params: {
			question: { type: 'string', description: '向用户提出的问题。针对你需要的信息，提出一个清晰、具体的问题。' },
			suggest: {
				type: 'string[]', description: `建议答案的选项，答案应在逻辑上承接问题。用户只能选择其中一个建议，每个建议必须:
        1. 具体、可操作且与当前用户问题直接相关
        2. 是问题的完整答案 - 用户不应需要提供额外信息或填补任何缺失细节。请勿包含带括号或圆括号的占位符。` },
		},
		required: ['question', 'suggest'],
		needApprove: false,
		example: `[用户问题] 在frontend-config.json文件中添加http-proxy配置信息

\`\`\`
<ask_followup_question>
<question>frontend-config.json文件的路径是什么?</question>
<suggest>./src/frontend-config.json</suggest>
<suggest>./config/frontend-config.json</suggest>
<suggest>./frontend-config.json</suggest>
</ask_followup_question>
\`\`\``
	},
	[ToolNameEnum.CTAGS_QUERY]: {
		name: ToolNameEnum.CTAGS_QUERY,
		description: `使用ctags工具输出给定的代码符号的文件路径，类型和开始行号和列号。`,
		params: {
			symbol: { type: 'string', description: '给定的代码符号' },
		},
		required: ['symbol'],
		needApprove: false,
		example: ''
	},
	[ToolNameEnum.CLANGD_QUERY]: {
		name: ToolNameEnum.CLANGD_QUERY,
		description: `使用clangd工具在给定的文件路径，给定的代码符号行号和列号，输出代码符号的引用。`,
		params: {
			filePath: { type: 'string', description: '给定的文件路径' },
			line: { type: 'number', description: '给定的代码符号的行号' },
			character: { type: 'number', description: '给定的代码符号的列号' },
		},
		required: ['filePath', 'line', 'character'],
		needApprove: false,
		example: ''
	},
	[ToolNameEnum.SHOW_SUMMARY]: {
		name: ToolNameEnum.SHOW_SUMMARY,
		description: `使用折叠标签给用户展示详细信息和总结`,
		params: {
			summary: { type: 'string', description: '总结' },
			detail: { type: 'string', description: '详细信息' },
		},
		required: ['summary', 'detail'],
		needApprove: false,
		example: ''
	},
	[ToolNameEnum.SHOW_CONTENT]: {
		name: ToolNameEnum.SHOW_CONTENT,
		description: `将生成的代码或答案展示给用户。该工具用于没有目标路径，仅做生成的代码或答案最后展示的情况。`,
		params: {
			content: { type: 'string', description: '待展示的内容。若答案中包含代码片段，使用Markdown代码块标注代码内容。' }
		},
		required: ['content'],
		needApprove: false,
		example: `[用户问题] main函数的实现逻辑是什么？

\`\`\`
<show_content>
<content>
main函数的实现逻辑如下：
\`\`\`cpp
int main() {
    int x = 0; // 初始化变量
    std::cout << x << std::endl;
    return 0;
}
\`\`\`
</content>
</show_content>
\`\`\``
	},
	[ToolNameEnum.CODEBASE_SEARCH]: {
		name: ToolNameEnum.CODEBASE_SEARCH,
		description: `从代码库中查找与搜索查询最相关的代码片段。
这是一个语义搜索工具，因此查询应该要求在语义上匹配所需内容。
除非有明确的理由使用自己的搜索查询，否则请使用用户的确切查询及其措辞。
它们的确切措辞/措辞通常对语义搜索查询有帮助。保持相同的确切问题格式也会有所帮助。`,
		params: {
			query: { type: 'string', description: '用于查找相关代码的搜索查询。除非有明确的理由，否则您应该重用用户的确切查询/最新消息及其措辞。' },
		},
		required: ['query'],
		needApprove: false,
		example: ''
	},
	[ToolNameEnum.EXEC_COMMAND]: {
		name: ToolNameEnum.EXEC_COMMAND,
		description: `执行终端命令并返回执行结果。该工具在**单次响应中只允许调用一次**。如需执行多条命令，请分步骤顺序迭代调用。`,
		params: {
			workdir: { type: 'string', description: 'Path to execute command.' },
			command: { type: 'string', description: 'Executed commands.' },
		},
		required: ['command'],
		needApprove: true,
		example: ''
	},
	[ToolNameEnum.GREP_SEARCH]: {
		name: ToolNameEnum.GREP_SEARCH,
		description: `快速的基于文本的正则表达式搜索，可在文件或目录中找到精确的模式匹配，利用ripgrep命令进行高效搜索。
结果将以ripgrep的样式格式化，并可配置为包括行号和内容。
为避免输出过多，结果上限为50个匹配。
使用包含或排除模式按文件类型或特定路径过滤搜索范围。

这最适合查找精确的文本匹配或正则表达式模式。
在查找特定字符串或模式方面比语义搜索更精确。
当我们知道在某些情况下要搜索的确切符号/函数名称等时，这比语义搜索更好。目录/文件类型集。`,
		params: {
			query: { type: 'string', description: '要搜索的非空关键字，只能配置一个关键字' },
			case_sensitive: { type: 'boolean', description: '搜索是否应区分大小写' },
			exclude_pattern: { type: 'string', description: '要排除文件的 Glob 模式, 配置多个以逗号分隔' },
			include_pattern: { type: 'string', description: `要包含的文件的 Glob 模式(例如 TypeScript 文件的 '*.ts'), 配置多个以逗号分隔` },
			explanation: { type: 'string', description: '一句话解释为何使用此工具，以及它如何有助于实现目标。' },
		},
		required: ['query', 'case_sensitive', 'exclude_pattern', 'include_pattern', 'explanation'],
		needApprove: false,
		example: `
\`\`\`
<grep_search>
<query>main</query>
<case_sensitive>true</case_sensitive>
<exclude_pattern>*.py</exclude_pattern>
<include_pattern>*.ts</include_pattern>
<explanation>搜索main函数</explanation>
</grep_search>
\`\`\``
	},
	[ToolNameEnum.CALL_TRACE_QUERY]: {
		name: ToolNameEnum.CALL_TRACE_QUERY,
		description: `查询给定的代码符号的调用链和依赖关系。`,
		params: {
			relative_path: { type: 'string', description: '给定的代码符号的相对路径' },
			function: { type: 'string', description: '给定的代码符号的函数名，函数名要真实存在' },
		},
		required: ['relative_path', 'function'],
		needApprove: false,
		example: ''
	},
} satisfies { [key in ToolNameEnum]: InternalToolInfo };

export type ToolName = ToolNameEnum;

export const toolNamesSet = new Set<string>(Object.values(ToolNameEnum));
export const isAToolName = (toolName: string): toolName is ToolName => {
	const isAToolName = toolNamesSet.has(toolName);
	return isAToolName;
};


export type ToolParamNames<T extends ToolName> = keyof typeof codeseekTools[T]['params'];
export type ToolParamsObj<T extends ToolName> = { [paramName in ToolParamNames<T>]: unknown };


export type ToolCallParamsType = {
	[ToolNameEnum.READ_FILE]: { path: string, start_line_one_indexed: number, end_line_one_indexed: number, should_read_entire_file: boolean };
	[ToolNameEnum.EDIT_FILE]: { path: string, content: string, executeApply?: boolean };
	[ToolNameEnum.LIST_DIR]: { relative_workspace_path: string };
	[ToolNameEnum.FILE_SEARCH]: { query: string };
	[ToolNameEnum.DELETE_FILE]: { target_file: string };
	[ToolNameEnum.CREATE_FILE]: { path: string; content: string };
	[ToolNameEnum.APPROVE_REQUEST]: { content: string; command: string };
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: { question: string, suggest: string[] };
	[ToolNameEnum.CTAGS_QUERY]: { symbol: string; className?: string };
	[ToolNameEnum.CLANGD_QUERY]: { filePath: string, line: number, character: number };
	[ToolNameEnum.SHOW_SUMMARY]: { summary: string, detail: string };
	[ToolNameEnum.EXEC_COMMAND]: { workdir?: string, command: string };
	[ToolNameEnum.SHOW_CONTENT]: { content: string };
	[ToolNameEnum.CODEBASE_SEARCH]: { query: string };
	[ToolNameEnum.GREP_SEARCH]: { query: string, case_sensitive: boolean, exclude_pattern: string, include_pattern: string, explanation: string };
	[ToolNameEnum.CALL_TRACE_QUERY]: { relative_path: string, function: string };
};


export type ReadFileResultType = { uri: URI; fileContents: string; startLine: number; endLine: number };
export type EditFileResultType = { content: string };
export type ListFilesResultType = { rootURI: URI; children: DirectoryItem[] | null; hasPrevPage: boolean; itemsRemaining: number };
export type PathnameSearchResultType = { queryStr: string; uris: URI[]; };
export type SearchResultType = { queryStr: string; uris: URI[]; };
export type CreateFileResultType = {};
export type DeleteFileResultType = { content: string };
export type UpdateToFileResultType = { content: string; uri: URI };
export type ApproveRequestResultType = { content: string; response: AskReponseType.yesButtonClicked | AskReponseType.noButtonClicked };
export type AskFollowupQuestionResultType = { content: string, selectedIndex: number };
export type CommandResultType = { output: string };
export type CtagsQueryResultType = {
	rawLineContent: string;
	name: string;
	path: string;
	scopePath: string;
	line: number;
	kind: string;
	language: string;
	positions?: [number, number];
	scope?: string;
}[];
export type ClangdQueryResultType = {
	uri: string;
	range: {
		start: { line: number; character: number };
		end: { line: number; character: number };
	};
}[];
export type ShowContentType = { content: string };
export type CodebaseSearchToolResult = {
	searchResults: ISearchResult[];
}
export type ExecCommandResultType = {
	status: 'success' | 'failure',
	output: string
};
export type GrepSearchResultType = {
	result: {
		filePath: string;
		range: {
			startLineNumber: number;
			startColumn: number;
			endLineNumber: number;
			endColumn: number
		};
		content: string;
	}[]
};

export type CallTraceQueryResultType = {
	content: string;
};

export type ToolCallReturnType = {
	[ToolNameEnum.READ_FILE]: ReadFileResultType;
	[ToolNameEnum.EDIT_FILE]: EditFileResultType;
	[ToolNameEnum.LIST_DIR]: ListFilesResultType;
	[ToolNameEnum.FILE_SEARCH]: PathnameSearchResultType;
	[ToolNameEnum.DELETE_FILE]: DeleteFileResultType;
	[ToolNameEnum.CREATE_FILE]: CreateFileResultType;
	[ToolNameEnum.APPROVE_REQUEST]: ApproveRequestResultType;
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: AskFollowupQuestionResultType;
	[ToolNameEnum.CTAGS_QUERY]: CtagsQueryResultType;
	[ToolNameEnum.CLANGD_QUERY]: ClangdQueryResultType;
	[ToolNameEnum.SHOW_SUMMARY]: void;
	[ToolNameEnum.SHOW_CONTENT]: ShowContentType;
	[ToolNameEnum.CODEBASE_SEARCH]: CodebaseSearchToolResult;
	[ToolNameEnum.EXEC_COMMAND]: ExecCommandResultType;
	[ToolNameEnum.GREP_SEARCH]: GrepSearchResultType;
	[ToolNameEnum.CALL_TRACE_QUERY]: CallTraceQueryResultType;
};

export type DirectoryItem = {
	uri: URI;
	name: string;
	isDirectory: boolean;
	isSymbolicLink: boolean;
};

// export type ToolNeedApprove = { [T in ToolName & keyof TooLNeedApproveBool]: TooLNeedApproveBool[T] };
export type ToolFns = { [T in ToolName]: (p: ToolCallParamsType[T], additionalOpt: AdditionalOpts, callback?: () => any) => Promise<ToolCallReturnType[T]> };
export type ToolResultToString = { [T in ToolName]: (result: ToolCallReturnType[T]) => string };


export type AdditionalOpts = {
	toolCallId: string;
	cancelToken: CancellationToken;
	containerId: string;
	threadId: string;
	sessionId: string;
	chatId: string;
	businessEvent: string;
}

