/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { InputBox } from '../../../../../../../base/browser/ui/inputbox/inputBox.js'
import { ProviderNames, SettingName, displayInfoOfSettingName, providerNames, CodeseekModelInfo, globalSettingNames, customSettingNamesOfProvider, RefreshableProviderName, refreshableProviderNames, displayInfoOfProviderName, defaultProviderSettings, nonlocalProviderNames, localProviderNames, GlobalSettingName, FeatureNames, displayInfoOfFeatureName, isProviderNameDisabled, FeatureName, CodebaseModes, SettingsOfCodebase, defaultSettingsOfCodebase } from '../../../../common/codeseekSettingsTypes.js'
import ErrorBoundary from '../sidebar-tsx/ErrorBoundary.js'
import { CodeseekCustomDropdownBox, CodeseekInputBox, CodeseekInputBox2, CodeseekSwitch, SettingButton } from '../util/inputs.js'
import { useAccessor, useIsDark, useRefreshModelListener, useRefreshModelState, useSettingsState } from '../util/services.js'
import { X, RefreshCw, Loader2, Check, MoveRight } from 'lucide-react'
import { useScrollbarStyles } from '../util/useScrollbarStyles.js'
import { URI } from '../../../../../../../base/common/uri.js'
import { env } from '../../../../../../../base/common/process.js'
import { ModelDropdown } from './ModelDropdown.js'
import { ChatMarkdownRender } from '../markdown/ChatMarkdownRender.js'
import { WarningBox } from './WarningBox.js'
import { osType } from '../../../helpers/systemInfo.js'
import { getWorkspaceUri } from '../../../../common/helpers/path.js'


const SubtleButton = ({ onClick, text, icon, disabled }: { onClick: () => void, text: string, icon: React.ReactNode, disabled: boolean }) => {

	return <div className='flex items-center text-codeseek-fg-3 px-3 py-0.5 rounded-sm overflow-hidden gap-2 hover:bg-black/10 dark:hover:bg-gray-300/10'>
		<button className='flex items-center' disabled={disabled} onClick={onClick}>
			{icon}
		</button>
		<span>
			{text}
		</span>
	</div>
}

// models
const RefreshModelButton = ({ providerName }: { providerName: RefreshableProviderName }) => {

	const refreshModelState = useRefreshModelState()

	const accessor = useAccessor()
	const refreshModelService = accessor.get('IRefreshModelService')
	const metricsService = accessor.get('IMetricsService')

	const [justFinished, setJustFinished] = useState<null | 'finished' | 'error'>(null)

	useRefreshModelListener(
		useCallback((providerName2, refreshModelState) => {
			if (providerName2 !== providerName) return
			const { state } = refreshModelState[providerName]
			if (!(state === 'finished' || state === 'error')) return
			// now we know we just entered 'finished' state for this providerName
			setJustFinished(state)
			const tid = setTimeout(() => { setJustFinished(null) }, 2000)
			return () => clearTimeout(tid)
		}, [providerName])
	)

	const { state } = refreshModelState[providerName]

	const { title: providerTitle } = displayInfoOfProviderName(providerName)
	return <SubtleButton
		onClick={() => {
			refreshModelService.startRefreshingModels(providerName, { enableProviderOnSuccess: false, doNotFire: false })
			// metricsService.capture('Click', { providerName, action: 'Refresh Models' })
		}}
		text={justFinished === 'finished' ? `${providerTitle} Models are up-to-date!`
			: justFinished === 'error' ? `${providerTitle} not found!`
				: `Manually refresh ${providerTitle} models.`
		}
		icon={justFinished === 'finished' ? <Check className='stroke-green-500 size-3' />
			: justFinished === 'error' ? <X className='stroke-red-500 size-3' />
				: state === 'refreshing' ? <Loader2 className='size-3 animate-spin' />
					: <RefreshCw className='size-3' />
		}

		disabled={state === 'refreshing' || justFinished !== null}
	/>
}

const RefreshableModels = () => {
	const settingsState = useSettingsState()
	if (refreshableProviderNames.length === 0) return null

	const buttons = refreshableProviderNames.map(providerName => {
		if (!settingsState.settingsOfProvider[providerName]._didFillInProviderSettings) return null
		return <RefreshModelButton key={providerName} providerName={providerName} />
	})

	return <>
		{buttons}
	</>

}



const AddModelMenu = ({ onSubmit }: { onSubmit: () => void }) => {

	const accessor = useAccessor()
	const settingsStateService = accessor.get('ICodeseekSettingsService')

	const settingsState = useSettingsState()

	// const providerNameRef = useRef<ProviderNames | null>(null)
	const [providerName, setProviderName] = useState<ProviderNames | null>(null)

	const modelNameRef = useRef<HTMLTextAreaElement | null>(null)

	const [errorString, setErrorString] = useState('')


	return <>
		<div className='flex flex-col gap-1'>
		<div className='flex items-center gap-4'>

			{/* provider */}
			<CodeseekCustomDropdownBox
				options={providerNames.filter(i => i != ProviderNames.default)}
				selectedOption={providerName}
				onChangeOption={(pn) => setProviderName(pn)}
				getOptionDisplayName={(pn) => pn ? displayInfoOfProviderName(pn).title : '(null)'}
				getOptionDropdownName={(pn) => pn ? displayInfoOfProviderName(pn).title : '(null)'}
				getOptionsEqual={(a, b) => a === b}
				className={`max-w-44 w-full border border-codeseek-border-2 bg-codeseek-bg-1 text-codeseek-fg-3 text-root
					py-[4px] px-[6px]
				`}
				arrowTouchesText={false}
			/>

			{/* model */}
			<div className='max-w-44 w-full border border-codeseek-border-2 bg-codeseek-bg-1 text-codeseek-fg-3 text-root'>
				<CodeseekInputBox2
					placeholder='Model Name'
					className='mt-[2px] px-[6px] h-full w-full'
					ref={modelNameRef}
					multiline={false}
					enableMention={false}
				/>
			</div>

			{/* button */}
			<div className='flex items-center gap-3 mr-2'>
				<span className='cursor-pointer codicon codicon-close hover:bg-codeseek-bg-4 rounded-sm'
					onClick={() => {
						setProviderName(null)
						modelNameRef.current!.value = ''
						setErrorString('')
						onSubmit()
					}}
				/>
				<span
					className='cursor-pointer codicon codicon-add hover:bg-codeseek-bg-4 rounded-sm p-[1px]'
					style={{
						fontSize: '13px',
					}}
					onClick={() => {
						const modelName = modelNameRef.current?.value

						if (providerName === null) {
							setErrorString('Please select a provider.')
							return
						}
						if (!modelName) {
							setErrorString('Please enter a model name.')
							return
						}
						// if model already exists here
						if (settingsState.settingsOfProvider[providerName].models.find(m => m.modelName === modelName)) {
							setErrorString(`This model already exists under ${providerName}.`)
							return
						}

						settingsStateService.addModel(providerName, modelName)
						setErrorString('')
						onSubmit()
					}}
				/>
			</div>
		</div>
			{!errorString ? null : <div className='flex items-center text-red-500'>
				<span className='codicon codicon-error scale-75 text-red-500' />
				<span className='ml-1 text-sm'>{errorString}</span>
			</div>}
		</div>
	</>

}

const AddModelMenuFull = () => {
	const [open, setOpen] = useState(false)

	return <div className='py-1 my-4 pb-1 px-3 overflow-hidden '>
		{open ?
			<AddModelMenu onSubmit={() => { setOpen(false) }} />
			: <SettingButton className='' onClick={() => setOpen(true)}>Add Model</SettingButton>
		}
	</div>
}


export const ModelDump = () => {

	const accessor = useAccessor()
	const settingsStateService = accessor.get('ICodeseekSettingsService')

	const settingsState = useSettingsState()

	// a dump of all the enabled providers' models
	const modelDump: (CodeseekModelInfo & { providerName: ProviderNames, providerEnabled: boolean })[] = []
	for (let providerName of providerNames) {
		const providerSettings = settingsState.settingsOfProvider[providerName]
		// if (!providerSettings.enabled) continue
		modelDump.push(...providerSettings.models.map(model => ({ ...model, providerName, providerEnabled: !!providerSettings._didFillInProviderSettings })))
	}

	// sort by hidden
	modelDump.sort((a, b) => {
		return Number(b.providerEnabled) - Number(a.providerEnabled)
	})

	return <div className=''>
		{modelDump.map((m, i) => {
			const { isHidden, isSupportConfig, isAutodetected, isApplyModel, modelName, providerName, providerEnabled } = m
			const isNewProviderName = (i > 0 ? modelDump[i - 1] : undefined)?.providerName !== providerName

			const disabled = !providerEnabled

			return <div key={`${modelName}${providerName}`}
				className={`flex items-center justify-between gap-4 hover:bg-gray-400/10 py-1 px-3 rounded-sm overflow-hidden cursor-default truncate
				`}
			>
				{/* left part is width:full */}
				<div className={`flex-grow flex items-center gap-4`}>
					<span className='w-full max-w-32'>{isNewProviderName ? displayInfoOfProviderName(providerName).title : ''}</span>
					<span className='w-fit truncate'>{modelName}</span>
				</div>
				{/* right part is anything that fits */}
				<div className='flex items-center gap-4'>
					<span className='opacity-50 truncate'>{isAutodetected ? '(detected locally)' : isSupportConfig ? '' : '(custom model)'}</span>
					{isApplyModel ? <span className='opacity-50 truncate'>(apply model)</span> : null}
					{/* 对于Apply模型，隐藏switch按钮 */}
					{!isApplyModel && (
						<CodeseekSwitch
							value={disabled ? false : !isHidden}
							onChange={() => {
								settingsStateService.toggleModelHidden(providerName, modelName)
							}}
							disabled={disabled}
							size='sm'
						/>
					)}
					{/* Apply模型显示占位空间，保持布局一致 */}
					{isApplyModel && <div style={{ width: '32px', height: '20px' }}></div>}

					<div className={`w-5 flex items-center justify-center`}>
						{isSupportConfig ? null : <button onClick={() => { settingsStateService.deleteModel(providerName, modelName) }}><X className='size-4' /></button>}
					</div>
				</div>
			</div>
		})}
	</div>
}



// providers

const ProviderSetting = ({ providerName, settingName }: { providerName: ProviderNames, settingName: SettingName }) => {

	// const { title: providerTitle, } = displayInfoOfProviderName(providerName)

	const { title: settingTitle, placeholder, isPasswordField, subTextMd } = displayInfoOfSettingName(providerName, settingName) ?? {}
	if (settingTitle === undefined) return null
	const accessor = useAccessor()
	const codeseekSettingsService = accessor.get('ICodeseekSettingsService')

	let weChangedTextRef = false

	return <ErrorBoundary>
		<div className='my-1'>
			<CodeseekInputBox
				// placeholder={`${providerTitle} ${settingTitle} (${placeholder})`}
				placeholder={`${settingTitle} (${placeholder})`}
				className='bg-gray-200 dark:bg-gray-200/10'
				onChangeText={useCallback((newVal) => {
					if (weChangedTextRef) return
					codeseekSettingsService.setSettingOfProvider(providerName, settingName, newVal)
				}, [codeseekSettingsService, providerName, settingName])}

				// we are responsible for setting the initial value. always sync the instance whenever there's a change to state.
				onCreateInstance={useCallback((instance: InputBox) => {
					const syncInstance = () => {
						const settingsAtProvider = codeseekSettingsService.state.settingsOfProvider[providerName];
						const stateVal = settingsAtProvider[settingName as SettingName]

						// console.log('SYNCING TO', providerName, settingName, stateVal)
						weChangedTextRef = true
						instance.value = stateVal as string
						weChangedTextRef = false
					}

					syncInstance()
					const disposable = codeseekSettingsService.onDidChangeState(syncInstance)
					return [disposable]
				}, [codeseekSettingsService, providerName, settingName])}
				multiline={false}
				isPasswordField={isPasswordField}
			/>
			{subTextMd === undefined ? null : <div className='py-1 px-3 opacity-50 text-sm'>
				<ChatMarkdownRender noSpace string={subTextMd} />
			</div>}

		</div>
	</ErrorBoundary>
}

const SettingsForProvider = ({ providerName }: { providerName: ProviderNames }) => {
	const codeseekSettingsState = useSettingsState()

	const needsModel = isProviderNameDisabled(providerName, codeseekSettingsState) === 'addModel'

	// const accessor = useAccessor()
	// const codeseekSettingsService = accessor.get('ICodeseekSettingsService')

	// const { enabled } = codeseekSettingsState.settingsOfProvider[providerName]
	const settingNames = customSettingNamesOfProvider(providerName)

	const { title: providerTitle } = displayInfoOfProviderName(providerName)

	return <div className='my-4'>

		<div className='flex items-center w-full gap-4'>
			<h3 className='text-xl truncate'>{providerTitle}</h3>

			{/* enable provider switch */}
			{/* <CodeseekSwitch
				value={!!enabled}
				onChange={
					useCallback(() => {
						const enabledRef = codeseekSettingsService.state.settingsOfProvider[providerName].enabled
						codeseekSettingsService.setSettingOfProvider(providerName, 'enabled', !enabledRef)
					}, [codeseekSettingsService, providerName])}
				size='sm+'
			/> */}
		</div>

		<div className='px-0'>
			{/* settings besides models (e.g. api key) */}
			{settingNames.map((settingName, i) => {
				return <ProviderSetting key={settingName} providerName={providerName} settingName={settingName} />
			})}

			{needsModel ?
				providerName as string === 'ollama' ?
					<WarningBox text={`Please install an Ollama model. We'll auto-detect it.`} />
					: <WarningBox text={`Please add a model for ${providerTitle} (Models section).`} />
				: null}
		</div>
	</div >
}

const LocalProvider = () => {
	if (localProviderNames.length == 0) return;
	return <>
		<h2 className={`text-3xl mb-2 mt-12`}>Local Providers</h2>
		{/* <h3 className={`opacity-50 mb-2`>{`Keep your data private by hosting AI locally on your computer.`}</h3> */}
		{/* <h3 className={`opacity-50 mb-2`>{`Instructions:`}</h3> */}
		{/* <h3 className={`mb-2`>{`Codeseek can access any model that you host locally. We automatically detect your local models by default.`}</h3> */}
		<h3 className={`text-codeseek-fg-3 mb-2`}>{`Codeseek can access any model that you host locally. We automatically detect your local models by default.`}</h3>
		<div className='pl-4 opacity-50'>
			<span className={`text-sm mb-2`}><ChatMarkdownRender noSpace string={`1. Download [Ollama](https://ollama.com/download).`} /></span>
			<span className={`text-sm mb-2`}><ChatMarkdownRender noSpace string={`2. Open your terminal.`} /></span>
			<span className={`text-sm mb-2 select-text`}><ChatMarkdownRender noSpace string={`3. Run \`ollama run llama3.1:8b\`. This installs Meta's llama3.1 model which is best for chat and inline edits. Requires 5GB of memory.`} /></span>
			<span className={`text-sm mb-2 select-text`}><ChatMarkdownRender noSpace string={`4. Run \`ollama run qwen2.5-coder:1.5b\`. This installs a faster autocomplete model. Requires 1GB of memory.`} /></span>
			<span className={`text-sm mb-2`}><ChatMarkdownRender noSpace string={`Codeseek automatically detects locally running models and enables them.`} /></span>
			{/* TODO we should create UI for downloading models without user going into terminal */}
		</div>

		<CodeseekProviderSettings providerNames={localProviderNames} />
	</>
}

export const CodeseekProviderSettings = ({ providerNames }: { providerNames: ProviderNames[] }) => {
	const codeseekSettingsState = useSettingsState()
	const needConfigProvider = providerNames.filter(providerName => !codeseekSettingsState.settingsOfProvider[providerName].isDefault)
	if (needConfigProvider.length === 0) return null
	return <>
		<h2 className={`text-3xl mb-2 mt-12`}>Providers</h2>
		<h3 className={`text-codeseek-fg-3 mb-2`}>{`Codeseek can access models from Anthropic, OpenAI, OpenRouter, and more.`}</h3>
		{providerNames.filter(providerName => !codeseekSettingsState.settingsOfProvider[providerName].isDefault).map(providerName =>
			<SettingsForProvider key={providerName} providerName={providerName} />
		)}
	</>
}


type TabName = 'models' | 'general' | 'rules'
export const AutoRefreshToggle = () => {
	if (refreshableProviderNames.length === 0) return null
	const settingName: GlobalSettingName = 'autoRefreshModels'

	const accessor = useAccessor()
	const codeseekSettingsService = accessor.get('ICodeseekSettingsService')
	const metricsService = accessor.get('IMetricsService')

	const codeseekSettingsState = useSettingsState()

	// right now this is just `enabled_autoRefreshModels`
	const enabled = codeseekSettingsState.globalSettings[settingName]
	const text = `Automatically detect local providers and models (${refreshableProviderNames.map(providerName => displayInfoOfProviderName(providerName).title).join(', ')}).`
	return <SubtleButton
		onClick={() => {
			codeseekSettingsService.setGlobalSetting(settingName, !enabled)
			// metricsService.capture('Click', { action: 'Autorefresh Toggle', settingName, enabled: !enabled })
		}}
		text={text}
		icon={enabled ? <Check className='stroke-green-500 size-3' /> : <X className='stroke-red-500 size-3' />}
		disabled={false}
	/>
}

export const AIInstructionsBox = () => {
	const accessor = useAccessor()
	const codeseekSettingsService = accessor.get('ICodeseekSettingsService')
	const codeseekSettingsState = useSettingsState()
	return <CodeseekInputBox2
		className='min-h-[70px] p-3 rounded-sm'
		initValue={codeseekSettingsState.globalSettings.aiInstructions}
		placeholder={`Always reply in Chinese. `}
		multiline
		onChangeText={(newText) => {
			codeseekSettingsService.setGlobalSetting('aiInstructions', newText)
		}}
		enableMention={false}
	/>
}

export const ModelsTab = () => {
	return <>
		<h2 className={`text-3xl mb-2`}>Models</h2>
		<ErrorBoundary>
			<AutoRefreshToggle />
			<RefreshableModels />
			<div className='py-2' />
			<ModelDump />
			<AddModelMenuFull />
		</ErrorBoundary>

		<ErrorBoundary>
			<LocalProvider />
		</ErrorBoundary>

		<ErrorBoundary>
			<CodeseekProviderSettings providerNames={nonlocalProviderNames} />
		</ErrorBoundary>



		<h2 className={`text-3xl mb-2 mt-12`}>Feature Options</h2>
		<ErrorBoundary>
			{Object.values(FeatureNames).map(featureName =>
				([FeatureNames.CtrlL, FeatureNames.CtrlK] as FeatureName[]).includes(featureName) ? null :
					<div key={featureName}
						className='mb-2'
					>
						<h4 className={`text-codeseek-fg-3`}>{displayInfoOfFeatureName(featureName)}</h4>
						<ModelDropdown featureName={featureName} />
					</div>
			)}
		</ErrorBoundary>

	</>
}



// https://github.com/VSCodium/vscodium/blob/master/docs/index.md#migrating-from-visual-studio-code-to-vscodium
// https://code.visualstudio.com/docs/editor/extension-marketplace#_where-are-extensions-installed
type TransferFilesInfo = { from: URI, to: URI }[]
const transferTheseFilesOfOS = (os: 'mac' | 'windows' | 'linux' | null): TransferFilesInfo => {
	if (os === null)
		throw new Error(`One-click switch is not possible in this environment.`)
	if (os === 'mac') {
		const homeDir = env['HOME']
		if (!homeDir) throw new Error(`$HOME not found`)
		return [{
			from: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, 'Library', 'Application Support', 'Code', 'User', 'settings.json'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, 'Library', 'Application Support', 'Codeseek', 'User', 'settings.json'),
		}, {
			from: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, 'Library', 'Application Support', 'Code', 'User', 'keybindings.json'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, 'Library', 'Application Support', 'Codeseek', 'User', 'keybindings.json'),
		}, {
			from: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, '.vscode', 'extensions'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, '.codeseek-editor', 'extensions'),
		}]
	}

	if (os === 'linux') {
		const homeDir = env['HOME']
		if (!homeDir) throw new Error(`variable for $HOME location not found`)
		return [{
			from: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, '.config', 'Code', 'User', 'settings.json'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, '.config', 'Codeseek', 'User', 'settings.json'),
		}, {
			from: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, '.config', 'Code', 'User', 'keybindings.json'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, '.config', 'Codeseek', 'User', 'keybindings.json'),
		}, {
			from: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, '.vscode', 'extensions'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), homeDir, '.codeseek-editor', 'extensions'),
		}]
	}

	if (os === 'windows') {
		const appdata = env['APPDATA']
		if (!appdata) throw new Error(`variable for %APPDATA% location not found`)
		const userprofile = env['USERPROFILE']
		if (!userprofile) throw new Error(`variable for %USERPROFILE% location not found`)

		return [{
			from: URI.joinPath(URI.from({ scheme: 'file' }), appdata, 'Code', 'User', 'settings.json'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), appdata, 'Codeseek', 'User', 'settings.json'),
		}, {
			from: URI.joinPath(URI.from({ scheme: 'file' }), appdata, 'Code', 'User', 'keybindings.json'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), appdata, 'Codeseek', 'User', 'keybindings.json'),
		}, {
			from: URI.joinPath(URI.from({ scheme: 'file' }), userprofile, '.vscode', 'extensions'),
			to: URI.joinPath(URI.from({ scheme: 'file' }), userprofile, '.codeseek-editor', 'extensions'),
		}]
	}

	throw new Error(`os '${osType}' not recognized`)
}


let transferTheseFiles: TransferFilesInfo = []
let transferError: string | null = null

try { transferTheseFiles = transferTheseFilesOfOS(osType) }
catch (e) { transferError = e + '' }

const OneClickSwitchButton = () => {
	const accessor = useAccessor()
	const fileService = accessor.get('IFileService')

	const [state, setState] = useState<{ type: 'done', error?: string } | { type: | 'loading' | 'justfinished' }>({ type: 'done' })

	if (transferTheseFiles.length === 0)
		return <>
			<WarningBox text={transferError ?? `One-click switch not available.`} />
		</>



	const onClick = async () => {

		if (state.type !== 'done') return

		setState({ type: 'loading' })

		let errAcc = ''
		for (let { from, to } of transferTheseFiles) {
			console.log('transferring', from, to)
			// not sure if this can fail, just wrapping it with try/catch for now
			try { await fileService.copy(from, to, true) }
			catch (e) { errAcc += e + '\n' }
		}
		const hadError = !!errAcc
		if (hadError) {
			setState({ type: 'done', error: errAcc })
		}
		else {
			setState({ type: 'justfinished' })
			setTimeout(() => { setState({ type: 'done' }); }, 3000)
		}
	}

	return <>
		<SettingButton disabled={state.type !== 'done'} onClick={onClick}>
			{state.type === 'done' ? 'Transfer my Settings'
				: state.type === 'loading' ? 'Transferring...'
					: state.type === 'justfinished' ? 'Success!'
						: null
			}
		</SettingButton>
		{state.type === 'done' && state.error ? <WarningBox text={state.error} /> : null}
	</>
}


const GeneralTab = () => {
	const accessor = useAccessor()
	const codeseekSettingsService = accessor.get('ICodeseekSettingsService')
	const commandService = accessor.get('ICommandService')
	const codeseekSettingsState = useSettingsState()
	return <>
		{/* <div className=''>
			<h2 className={`text-3xl mb-2`}>One-Click Switch</h2>
			<h4 className={`text-codeseek-fg-3 mb-2`}>{`Transfer your settings from VS Code to Codeseek in one click.`}</h4>
			<OneClickSwitchButton />
		</div> */}

		<div className=''>
			<h2 className={`text-3xl mb-2`}>Built-in Settings</h2>
			<h4 className={`text-codeseek-fg-3 mb-2`}>{`IDE settings, keyboard settings, and theme customization.`}</h4>

			<div className='my-4'>
				<SettingButton onClick={() => { commandService.executeCommand('workbench.action.openSettings') }}>
					General Settings
				</SettingButton>
			</div>
			<div className='my-4'>
				<SettingButton onClick={() => { commandService.executeCommand('workbench.action.openGlobalKeybindings') }}>
					Keyboard Settings
				</SettingButton>
			</div>
			<div className='my-4'>
				<SettingButton onClick={() => { commandService.executeCommand('workbench.action.selectTheme') }}>
					Theme Settings
				</SettingButton>
			</div>
		</div>


		<div className='mt-12'>
			<h2 className={`text-3xl mb-2`}>Features</h2>
			<h4 className={`text-codeseek-fg-3 mb-2`}>{`Enable Or Disable Features`}</h4>

			<div className='flex flex-col gap-4'>
			<CodeseekSwitch value={codeseekSettingsState.globalSettings.enableCodeBase}
						onChange={() => {
							codeseekSettingsService.setGlobalSetting('enableCodeBase', !codeseekSettingsState.globalSettings.enableCodeBase)
						}}
						label={`Enable CodeBase`}
						disabled={false}
						size='sm'/>
			<CodeseekSwitch value={codeseekSettingsState.globalSettings.autoApprove}
						onChange={() => {
							codeseekSettingsService.setGlobalSetting('autoApprove', !codeseekSettingsState.globalSettings.autoApprove)
						}}
						label={`Auto Approve`}
						disabled={false}
						size='sm'/>
			</div>
		</div>

		{
			codeseekSettingsState.globalSettings.enableCodeBase ?
				<div className='mt-12'>
					<CodebaseIndexing />
				</div>
				: null
		}

		<div className='mt-12'>
			<h2 className={`text-3xl mb-2`}>C/C++ Development</h2>
			<h4 className={`text-codeseek-fg-3 mb-2`}>{`C/C++ 开发相关配置`}</h4>

			<div className='flex flex-col gap-4'>
				<div>
					<div className='text-sm mb-1'>Clangd Compile Commands Directory</div>
					<div className='flex items-center gap-2'>
						<CodeseekInputBox2
							className='p-2 rounded-sm bg-gray-200 dark:bg-gray-200/10 min-w-[300px]'
							initValue={codeseekSettingsState.globalSettings.clangdCompileCommandsDir}
							placeholder="compile_commands.json 目录路径"
							multiline={false}
							onChangeText={(newText) => {
								codeseekSettingsService.setGlobalSetting('clangdCompileCommandsDir', newText)
							}}
							enableMention={false}
						/>
					</div>
					<div className='text-codeseek-fg-3 text-xs mt-1'>指定包含 compile_commands.json 文件的目录路径，用于 clangd 语言服务器</div>
				</div>
			</div>
		</div>
	</>
}

export const CodebaseIndexing = () => {
	const accessor = useAccessor()
	const codeseekSettingsService = accessor.get('ICodeseekSettingsService')
	const codeseekSettingsState = useSettingsState()
	const [remoteProcess, setRemoteProcess] = useState(0)
	const [syncedFileNumber, setSyncedFileNumber] = useState(0)
	const [status, setStatus] = useState('idle')
	const currentRepoUri = useMemo(() => {
		const workspaceContextService = accessor.get('IWorkspaceContextService')
		return getWorkspaceUri(workspaceContextService).workspaceUri
	}, [accessor])

	useEffect(() => {
		if (!currentRepoUri) return
		const settings = codeseekSettingsState.settingsOfCodebase[currentRepoUri.fsPath]?.[CodebaseModes.Remote]
		setRemoteProcess(settings?.process ?? 0)
		setSyncedFileNumber(settings?.syncedFileNumber ?? 0)
		setStatus(settings?.status ?? 'idle')
	}, [codeseekSettingsState.settingsOfCodebase, currentRepoUri])

	const handleResyncIndex = useCallback(() => {
		codeseekSettingsService.reSyncCodebase()
	}, [codeseekSettingsService])

	const handleDeleteIndex = useCallback(() => {
		codeseekSettingsService.deleteCodebase()
	}, [codeseekSettingsService])

	const handlePauseIndex = useCallback(() => {
		codeseekSettingsService.pauseCodebase()
	}, [codeseekSettingsService])

	const handleResumeIndex = useCallback(() => {
		codeseekSettingsService.resumeCodebase()
	}, [codeseekSettingsService])

	return <>
		<h2 className={`text-3xl mb-2`}>Codebase Indexing</h2>
		<h4 className={`text-codeseek-fg-3 mb-2`}>{`Embeddings improve your codebase-wide answers.`}</h4>

		<div className='mt-4'>
			<div className='flex items-center justify-between'>
				<span className='text-codeseek-fg-3'>Synced({syncedFileNumber} files)</span>
			</div>
			<div className='flex items-center gap-2'>
				<div className='w-full h-2 bg-gray-200 dark:bg-white rounded-full overflow-hidden'>
					<div
						className='h-full bg-blue-500 transition-all duration-300'
						style={{ width: `${remoteProcess * 100}%` }}
					/>
				</div>
				<span className='text-codeseek-fg-3 whitespace-nowrap'>{`${Math.round(remoteProcess * 100)}%`}</span>
			</div>
			<div className='flex gap-2 mt-1'>
				{status === 'indexing' && (
					<SettingButton
						onClick={handlePauseIndex}
						className=''
					>
						Pause Index
					</SettingButton>
				)}
				{status === 'paused' && (
					<SettingButton
						onClick={handleResumeIndex}
						className=''
					>
						Resume Index
					</SettingButton>
				)}
				{(status === 'idle' || status === 'completed') && (
					<SettingButton
						onClick={handleResyncIndex}
						className=''
					>
						Resync Index
					</SettingButton>
				)}
				<SettingButton
					onClick={handleDeleteIndex}
					className='bg-transparent hover:bg-gray-500/20 dark:hover:bg-gray-200/5 text-codeseek-fg-1'
				>
					Delete Index
				</SettingButton>
			</div>
		</div>
	</>
}

const RulesTab = () => {
	const accessor = useAccessor()
	const codeseekSettingsService = accessor.get('ICodeseekSettingsService')
	const codeseekSettingsState = useSettingsState()
	return <>
		<h2 className={`text-3xl mb-2`}>Rules</h2>
		<h4 className={`text-codeseek-fg-3 mb-2`}>{`Rules provide more context to AI models to help them follow your personal preferences and operate more efficiently in your codebase.`}</h4>

		<div className='flex flex-col gap-1 mt-4'>
			<h2 className='text-lg'>User Rules</h2>
			<p className='text-codeseek-fg-3'>{`These preferences get sent to the AI on all chats, composers and Ctrl-K sessions.`}</p>
			<div className='border border-codeseek-border-3 hover:border-codeseek-border-1 rounded-sm px-2 py-1 mt-2'>
				<CodeseekInputBox2
					className='min-h-[100px] dark:text-white light:text-black'
					placeholder={`e.g., &quot;Always output answers in Portuguese, prefer terse answers, always use functional code. `}
					multiline
					onChangeText={(newText) => {
						codeseekSettingsService.setGlobalSetting('userRules', newText)
					}}
					initValue={codeseekSettingsState.globalSettings.userRules}
					enableMention={false}
				/>
			</div>
		</div>
	</>
}

// full settings

export const Settings = () => {
	const isDark = useIsDark()

	const [tab, setTab] = useState<TabName>('general')

	const containerRef = useRef<HTMLDivElement | null>(null)
	useScrollbarStyles(containerRef)

	return <div className={`@@codeseek-scope ${isDark ? 'dark' : ''}`} style={{ height: '100%', width: '100%' }}>
		<div ref={containerRef} className='w-full h-full select-none flex flex-col'>
			<h1 className='text-2xl w-full px-2 py-3 border-b border-codeseek-border-3 flex-shrink-0'>{`Flow Settings`}</h1>

			<div className='flex flex-1 overflow-hidden'>
				<div className='flex mx-1 w-full h-full overflow-auto'>
					{/* tabs */}
					<div className='flex-shrink-0 w-full max-w-32 border-r border-codeseek-border-3 sticky top-0'>
						<button className={`flex w-full items-center text-left p-1 mr-1 my-0.5 rounded-sm overflow-hidden ${tab === 'general' ? 'bg-black/10 dark:bg-gray-200/10' : ''} hover:bg-black/10 hover:dark:bg-gray-200/10 active:bg-black/10 active:dark:bg-gray-200/10 `}
							onClick={() => { setTab('general') }}
						>
							<span className='flex items-center codicon codicon-settings px-2'/>
							<span>General</span>
						</button>
						<button className={`flex w-full items-center text-left p-1 mr-1 my-0.5 rounded-sm overflow-hidden ${tab === 'models' ? 'bg-black/10 dark:bg-gray-200/10' : ''} hover:bg-black/10 hover:dark:bg-gray-200/10 active:bg-black/10 active:dark:bg-gray-200/10 `}
							onClick={() => { setTab('models') }}
						>
							<span className='flex items-center codicon codicon-graph px-2'/>
							<span>Models</span>
						</button>

						<button className={`flex w-full items-center text-left p-1 mr-1 my-0.5 rounded-sm overflow-hidden ${tab === 'rules' ? 'bg-black/10 dark:bg-gray-200/10' : ''} hover:bg-black/10 hover:dark:bg-gray-200/10 active:bg-black/10 active:dark:bg-gray-200/10 `}
							onClick={() => { setTab('rules') }}
						>
							<span className='flex items-center codicon codicon-symbol-ruler px-2'/>
							<span>Rules</span>
						</button>
					</div>

					{/* content */}
					<div className='flex-1 mx-4 mt-3'>
						<div className={`${tab !== 'models' ? 'hidden' : ''}`}>
							<ModelsTab />
						</div>

						<div className={`${tab !== 'general' ? 'hidden' : ''}`}>
							<GeneralTab />
						</div>
						<div className={`${tab !== 'rules' ? 'hidden' : ''}`}>
							<RulesTab />
						</div>

						{/* 底部占位空间 */}
						<div className="h-24"></div>
					</div>
				</div>
			</div>
		</div>
	</div>
}
