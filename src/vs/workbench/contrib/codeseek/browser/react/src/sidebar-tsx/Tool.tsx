import React, {useCallback, useEffect, useRef, useState } from 'react';
import { ToolMessage } from '../../../chatThreadType.js';
import { AskReponseType, ToolCallResultType, ToolCallReturnType, ToolCallStatus, ToolCallType, ToolNameEnum } from '../../../../../../../workbench/contrib/codeseek/common/toolsServiceTypes.js';
import { useAccessor } from '../util/services.js';
import { convertFilePathToUri, getWorkspaceUri } from '../../../../common/helpers/path.js';
import { ChatMarkdownRender } from '../markdown/ChatMarkdownRender.js';
import { ChevronRight, Pencil, X } from 'lucide-react';
import { ChatMessageLocation } from '../../../aiRegexService.js';
import { ToolCallParamsType, ToolName } from '../../../../common/toolsServiceTypes.js';
import { FileKind } from '../../../../../../../platform/files/common/files.js';
import { URI } from '../../../../../../../base/common/uri.js';
import { BlockCode } from '../markdown/BlockCode.js';
import { ButtonStop, VSCodeFileIcon } from './SidebarChat.js';
import { AskFollowupQuestion, ClickCallBackProps } from '../tool/AskFollowupQuestion.js';
import { TextAreaFns } from '../util/inputs.js';


export const getBasename = (pathStr: string) => {
	if (!pathStr) {
		return '';
	}
	// 'unixify' path
	pathStr = pathStr.replace(/[/\\]+/g, '/') // replace any / or \ or \\ with /
	const parts = pathStr.split('/') // split on /
	return parts[parts.length - 1]
}

export type FileItemListProps = {
	files: {
		uri: URI,
		range?: {
			startLineNumber: number;
			startColumn: number;
			endLineNumber: number;
			endColumn: number
		},
	}[]
	className: {
		hover: string,
		normal: string
	}
}

export const FileItemList = ({ files, className }: FileItemListProps) => {
	return (
		<div className="w-full max-h-[100px] overflow-y-auto overflow-x-hidden"
			style={{
				scrollbarWidth: 'thin',
				scrollbarColor: 'var(--vscode-scrollbarSlider-background) transparent'
			}}>
			{files.map(({ uri, range }, i) => (
				<FileItem
					key={i}
					uri={uri}
					range={range}
					className={className}
				/>
			))}
		</div>
	)
}

// FileItem组件的类型定义
interface FileItemProps {
	uri: URI;
	range?: {
		startLineNumber: number;
		startColumn: number;
		endLineNumber: number;
		endColumn: number;
	};
	className: {
		hover: string;
		normal: string;
	};
}

// 单独的FileItem组件，每个组件有自己的hover状态
const FileItem = ({ uri, range, className }: FileItemProps) => {
	const [isHovered, setIsHovered] = useState<boolean>(false)
	const accessor = useAccessor()
	const commandService = accessor.get('ICommandService')
	return (
		<div className="pl-4">
			<div className="flex items-center gap-1">
				<VSCodeFileIcon uri={uri} fileKind={FileKind.FILE} />
				<div
					className={`cursor-pointer`}
					onClick={(e) => {
						e.preventDefault();
						if (uri) {
							commandService.executeCommand('vscode.open', uri, {
								selection: {
									startLineNumber: range?.startLineNumber,
									startColumn: range?.startColumn,
									endLineNumber: range?.endLineNumber,
									endColumn: range?.endColumn
								},
								preserveFocus: false,
								preview: true,
							}).then(() => {
								commandService.executeCommand('revealLine', {
									lineNumber: range?.startLineNumber,
									at: 'center'
								});
							});
						}
					}}
				>
					<div className={`flex items-center w-full overflow-hidden text-ellipsis whitespace-nowrap ${isHovered ? className.hover : className.normal}`}
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
					>
						<div className="flex items-center text-md truncate max-w-[50px]">
							{getBasename(uri.path)}
						</div>
						<div className="flex items-center pl-2 text-xs overflow-hidden opacity-80">
							<div style={{ direction: 'ltr', unicodeBidi: 'embed' }} className="truncate">{uri.fsPath}</div>
							<div className="ml-[4px] opacity-70">{range ? ` L${range.startLineNumber}-${range.endLineNumber}` : ''}</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	)
}

type ToolReusltToComponent = { [T in ToolName]: (props: { message: ToolMessage, onSuggestionClick?: ClickCallBackProps }, messageIdx: number, containerId?: string, threadId?: string) => React.ReactNode }
interface ToolResultProps {
	title: string;
	toolCallResult: ToolCallResultType;
	actionNumResults?: number;
	children?: React.ReactNode;
	showParamHtml?: React.ReactElement;
	icon?: string;
	actionParam? : string;
	onclick?: (e: React.MouseEvent<HTMLDivElement>) => void;
}

const ToolResult = ({
	title,
	toolCallResult,
	actionNumResults,
	children,
	showParamHtml,
	actionParam,
	icon,
	onclick
}: ToolResultProps) => {
	const [isExpanded, setIsExpanded] = useState<boolean>(false);
	const [isHovered, setIsHovered] = useState<boolean>(false);

	const isDropdown = !!children

	return toolCallResult.status === ToolCallStatus.failure ? (
		<div className="select-none">
			<div className="px-2 pt-1 flex text-md text-vscode-error-fg opacity-1 transition-opacity duration-100">
				<div className="px-1.5 flex-shrink-0 mt-1">
					<span className="codicon codicon-warning"
						style={{
							fontSize: '13px',
							color: 'var(--vscode-errorForeground)',
						}}
					></span>
				</div>
				<div className="py-0.5 flex-grow">
					{toolCallResult.error}
				</div>
			</div>
		</div>
	) : (
		<div className="select-none text-codeseek-fg-3 text-xs cursor-pointer"
			onClick={onclick}
		>
			<div className={`px-2 pt-2 pb-1 bg-codeseek-bg-tool ${isExpanded ? 'text-codeseek-fg-1' : 'hover:text-codeseek-fg-1'}`}
				style={{
					transition: 'opacity 0.3s ease'
				}}
			>
				<div
					className={`flex items-center min-h-[20px] ${isDropdown ? 'px-1 hover:brightness-125 duration-150' : 'mx-1'} ${isExpanded ? 'text-codeseek-fg-1' : ''}`}
					onClick={() => children && setIsExpanded(!isExpanded)}
					onMouseEnter={() => setIsHovered(true)}
					onMouseLeave={() => setIsHovered(false)}
				>
					<div className="w-5 h-5 flex items-center justify-center flex-shrink-0 mr-1">
						{isDropdown && (isHovered || isExpanded) ? (
							<ChevronRight size={12} style={
									{
										color: 'var(--vscode-foreground)',
										opacity: (isHovered || isExpanded) ? '1' : '0.6',
										transition: 'opacity 0.3s ease'
									}
								}
								className={`transition-transform duration-100 ease-[cubic-bezier(0.4,0,0.2,1)] ${isExpanded ? 'rotate-90' : ''}`}
							/>
						) : icon && (
							<span className={`flex items-center justify-center ${icon}`}
								 style={{ transition: 'opacity 0.3s ease', fontSize: '14px', opacity: (isHovered) ? '1' : '0.6', color: 'var(--vscode-foreground)' }}
							></span>
						)}
					</div>
					<div className="flex items-center flex-wrap gap-x-1 relative flex-grow">
						<span className="text-md">{title}</span>
						{showParamHtml ? (
							<div className="flex items-end">
								{showParamHtml}
							</div>
						) : actionParam ? (
							<span className="flex items-end text-codeseek-fg-4 italic">{`"`}{actionParam}{`"`}</span>
						) : null}
						{actionNumResults !== undefined && (
							<span className="text-codeseek-fg-4 flex items-end">
								{`(`}{actionNumResults}{` result`}{actionNumResults !== 1 ? 's' : ''}{`)`}
							</span>
						)}
						{toolCallResult.status === ToolCallStatus.executing && (
							<span className="ml-auto mr-2 mt-0.5 animate-spin">
								<span className="codicon codicon-loading" style={{ fontSize: '9px' }}></span>
							</span>
						)}
					</div>
				</div>
			</div>
			{children && (
				<div
					className={`select-text px-2 py-0.5 transition-all duration-200 ease-in-out ${isExpanded ? 'max-h-[500px] opacity-100 overflow-y-auto text-codeseek-fg-1' : 'hidden'}`}
				>
					{children}
				</div>
			)}
		</div>
	)
};

export const ToolResultToComponent: ToolReusltToComponent = {
	[ToolNameEnum.READ_FILE]: ({ message }, messageIdx: number) => {
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')
		const workspaceService = accessor.get('IWorkspaceContextService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.READ_FILE]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.READ_FILE]
		const showParamHtml = (
			<div className="flex items-center gap-x-1">
				<VSCodeFileIcon uri={URI.file(params.path)} fileKind={FileKind.FILE} />
				<span className="italic">
					{getBasename(params.path)}{toolCallReturn?.startLine ? `:${toolCallReturn?.startLine}-${toolCallReturn?.endLine}` : ''}
				</span>
			</div>
		)
		return (
			<ToolResult
				icon="codicon codicon-eye"
				title="Read file"
				showParamHtml={showParamHtml}
				toolCallResult={message.toolCallResult}
				onclick={(e) => {
					e.preventDefault();
					if (params.path) {
						const uri = convertFilePathToUri(params.path, workspaceService);
						const line = toolCallReturn?.startLine || 1;
						commandService.executeCommand('vscode.open', uri, {
							selection: { startLineNumber: line, startColumn: 1, endLineNumber: line, endColumn: 1 },
							preserveFocus: false, // 确保编辑器获得焦点
							preview: true,
						}).then(() => {
							// 使用组合命令确保视图滚动和光标定位
							commandService.executeCommand('revealLine', {
								lineNumber: line,
								at: 'center' // 将目标行居中显示
							}).then(() => {
								// 确保光标定位并可见
								commandService.executeCommand('editor.action.goToLocations',
									uri,
									line,
									1,
									[],
									'goto'
								);
							});
						});
					}
				}}
			/>
		)
	},
	[ToolNameEnum.LIST_DIR]: ({ message }, messageIdx: number) => {
		const accessor = useAccessor()
		const workspaceService = accessor.get('IWorkspaceContextService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.LIST_DIR]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.LIST_DIR]
		let dirPath = params.relative_workspace_path;
		if (dirPath.endsWith('/')){
			dirPath = dirPath.slice(0, -1);
		}
		let folderName = getBasename(dirPath)
		if (folderName === '.') {
			const workspaceFolder = getWorkspaceUri(workspaceService).workspaceName
			if (workspaceFolder) {
				folderName = workspaceFolder;
			}
		}
		return <ToolResult
			icon='codicon codicon-folder !text-[13px]'
			title="List folder"
			actionParam={`${folderName}`}
			actionNumResults={toolCallReturn?.children?.length}
			toolCallResult={message.toolCallResult}
		>
			{Array.isArray(toolCallReturn?.children) && toolCallReturn?.children?.length > 0 ?
				toolCallReturn?.children?.map((item, i) => (
					<div key={i} className="pl-4 mb-[2px]">
						{item.isDirectory ? (
						<div className="flex items-center gap-x-2 px-1">
							<span className="codicon codicon-folder" style={{ fontSize: '10px' }}></span>
							<span>{item.name}/</span>
						</div>
					) : (
						<div className="flex items-center gap-x-1">
							<VSCodeFileIcon uri={item.uri} fileKind={FileKind.FILE} />
							<span>{item.name}</span>
						</div>
					)}
					</div>
				))
				:
				null
			}
		</ToolResult>
	},
	[ToolNameEnum.FILE_SEARCH]: ({ message }, messageIdx: number) => {
		const accessor = useAccessor()
		const workspaceService = accessor.get('IWorkspaceContextService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.FILE_SEARCH]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.FILE_SEARCH]
		return <ToolResult
			icon="codicon codicon-search"
			title="File Search"
			actionParam={params.query}
			actionNumResults={Array.isArray(toolCallReturn?.uris) ? toolCallReturn?.uris.length : 0}
			toolCallResult={message.toolCallResult}
		>
			{Array.isArray(toolCallReturn?.uris) && toolCallReturn?.uris.length > 0 ?
				<FileItemList files={toolCallReturn?.uris.map(uri => ({ uri: uri }))} className={{ hover: 'text-codeseek-fg-1', normal: 'text-codeseek-fg-3' }} />
				:
				null
			}
		</ToolResult>
	},
	[ToolNameEnum.GREP_SEARCH]: ({ message }, messageIdx: number) => {
		const accessor = useAccessor()
		const workspaceService = accessor.get('IWorkspaceContextService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.GREP_SEARCH]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.GREP_SEARCH]
		return <ToolResult
			icon="codicon codicon-search"
			title="Grep Search"
			actionParam={params.query}
			actionNumResults={Array.isArray(toolCallReturn?.result) ? toolCallReturn?.result.length : 0}
			toolCallResult={message.toolCallResult}
		>
			{Array.isArray(toolCallReturn?.result) && toolCallReturn?.result.length > 0 ?
				<FileItemList files={toolCallReturn?.result.map(r => ({ uri: convertFilePathToUri(r.filePath, workspaceService), range: r.range }))} className={{ hover: 'text-codeseek-fg-1', normal: 'text-codeseek-fg-3' }} />
				:
				null
			}
		</ToolResult>
	},
	[ToolNameEnum.CREATE_FILE]: ({ message }, messageIdx: number) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CREATE_FILE]
		return <ToolResult
			icon="codicon codicon-file"
			title="Create file"
			actionParam={params.path}
			toolCallResult={message.toolCallResult}
		>
		</ToolResult>
	},
	[ToolNameEnum.APPROVE_REQUEST]: ({ message }, messageIdx: number) => {
		return null
	},
	[ToolNameEnum.ASK_FOLLOWUP_QUESTION]: ({ message, onSuggestionClick }, messageIdx: number) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.ASK_FOLLOWUP_QUESTION]
		const result = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.ASK_FOLLOWUP_QUESTION]
		return <AskFollowupQuestion params={params} result={result} onSuggestionClick={onSuggestionClick} />
	},
	[ToolNameEnum.CTAGS_QUERY]: ({ message }, messageIdx: number) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CTAGS_QUERY]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.CTAGS_QUERY]
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')

		return <ToolResult
			icon="codicon codicon-search"
			title="使用 ctags 工具查询符号定义"
			actionParam={params.symbol}
			actionNumResults={Array.isArray(toolCallReturn) ? toolCallReturn.length : 0}
			toolCallResult={message.toolCallResult}
		>
			<div>
				{Array.isArray(toolCallReturn) && toolCallReturn.length > 0 ? (
					toolCallReturn.map((symbolDefinition, i) => (
						<div key={i} className="pl-2 py-1 mb-1">
							<div className="flex items-center gap-1">
								{symbolDefinition.path && (
									<VSCodeFileIcon uri={URI.file(symbolDefinition.path)} fileKind={FileKind.FILE} />
								)}
								<a
									href="#"
									className="text-codeseek-accent hover:underline"
									onClick={(e) => {
										e.preventDefault();
										if (symbolDefinition.path) {
											const uri = URI.file(symbolDefinition.path);
											const line = symbolDefinition.line || (symbolDefinition.positions && symbolDefinition.positions[1]) || 1;
											commandService.executeCommand('vscode.open', uri, {
												selection: { startLineNumber: line, startColumn: 1, endLineNumber: line, endColumn: 1 },
												preserveFocus: false, // 确保编辑器获得焦点
												preview: true,
											}).then(() => {
												// 使用组合命令确保视图滚动和光标定位
												commandService.executeCommand('revealLine', {
													lineNumber: line,
													at: 'center' // 将目标行居中显示
												}).then(() => {
													// 确保光标定位并可见
													commandService.executeCommand('editor.action.goToLocations',
														uri,
														line,
														1,
														[],
														'goto'
													);
												});
											});
										}
									}}
								>
									{symbolDefinition.name}
								</a>
								<span>
									{symbolDefinition.kind && `(${symbolDefinition.kind})`}
								</span>
							</div>
							{symbolDefinition.path && (
								<div
									className="pl-4"
									title={symbolDefinition.path}
								>
									{getBasename(symbolDefinition.path)}
									{symbolDefinition.line && `:${symbolDefinition.line}`}
								</div>
							)}
						</div>
					))
				) : (
					<div className="pl-2 py-1 italic">
						没有找到相关符号
					</div>
				)}
			</div>
		</ToolResult>
	},
	[ToolNameEnum.CLANGD_QUERY]: ({ message }, messageIdx: number) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CLANGD_QUERY]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.CLANGD_QUERY]
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')

		return <ToolResult
			icon="codicon codicon-search"
			title="使用 clangd 工具查询符号定义"
			actionParam={`${params.filePath}:${params.line}:${params.character}`}
			actionNumResults={Array.isArray(toolCallReturn) ? toolCallReturn.length : 0}
			toolCallResult={message.toolCallResult}
		>
			<div>
				<div className="max-h-[300px] overflow-y-auto pr-1" style={{ scrollbarWidth: 'thin' }}>
					{Array.isArray(toolCallReturn) && toolCallReturn.length > 0 ? (
						toolCallReturn.map((reference, i) => (
							<div key={i} className="pl-2 py-1 mb-1">
								<div className="flex items-center gap-1">
									{reference.uri && (
										<VSCodeFileIcon uri={URI.parse(reference.uri)} fileKind={FileKind.FILE} />
									)}
									<a
										href="#"
										className="text-codeseek-accent hover:underline"
										onClick={(e) => {
											e.preventDefault();
											if (reference.uri) {
												const uri = URI.parse(reference.uri);
												const line = reference.range.start.line + 1 || 1;
												const character = reference.range.start.character + 1 || 1;

												commandService.executeCommand('vscode.open', uri, {
													selection: {
														startLineNumber: line,
														startColumn: character,
														endLineNumber: line,
														endColumn: character
													},
													preserveFocus: false,
													preview: true,
												}).then(() => {
													// 确保视图滚动和光标定位
													commandService.executeCommand('revealLine', {
														lineNumber: line,
														at: 'center' // 将目标行居中显示
													});
												});
											}
										}}
									>
										{getBasename(URI.parse(reference.uri).path)}:{reference.range.start.line + 1}:{reference.range.start.character + 1}
									</a>
								</div>
								{reference.uri && (
									<div className="pl-4 truncate" title={reference.uri}>
										{URI.parse(reference.uri).path}
									</div>
								)}
							</div>
						))
					) : (
						<div className="pl-2 py-1 italic">
							No results found
						</div>
					)}
				</div>
			</div>
		</ToolResult>
	},
	[ToolNameEnum.SHOW_SUMMARY]: ({ message }, messageIdx: number, containerId?: string, threadId?: string) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.SHOW_SUMMARY]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.SHOW_SUMMARY]
		const accessor = useAccessor()
		const chatThreadsService = accessor.get('IChatThreadService')
		const toolMarkdownIdOffset = 10_000;

		const chatMessageLocation: ChatMessageLocation = {
			containerId: containerId!,
			threadId: threadId!,
			messageIdx: messageIdx! + toolMarkdownIdOffset,
		}

		return (
			<ToolResult
				icon="codicon codicon-fold"
				title={params.summary}
				toolCallResult={message.toolCallResult}
			>
				<ChatMarkdownRender string={params.detail ?? ''} chatMessageLocation={chatMessageLocation} />
			</ToolResult>
		);
	},
	[ToolNameEnum.SHOW_CONTENT]: ({ message }, messageIdx: number, containerId?: string, threadId?: string) => {
		return null;
	},
	[ToolNameEnum.CODEBASE_SEARCH]: ({ message }, messageIdx: number) => {
		const accessor = useAccessor()
		const commandService = accessor.get('ICommandService')
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CODEBASE_SEARCH]
		const toolCallReturn = message.toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.CODEBASE_SEARCH]
		return <ToolResult
			icon="codicon codicon-search"
			title="Search Codebase"
			actionParam={params.query}
			toolCallResult={message.toolCallResult}
			actionNumResults={Array.isArray(toolCallReturn?.searchResults) ? toolCallReturn.searchResults.length : 0}
		>
			{Array.isArray(toolCallReturn?.searchResults) && toolCallReturn?.searchResults?.length > 0 ? (
				<FileItemList files={toolCallReturn.searchResults.map(result => ({ uri: result.uri, range: result.range }))} className={{ hover: 'text-codeseek-fg-1', normal: 'text-codeseek-fg-3' }} />
			) : (
				null
			)}
		</ToolResult>
	},
	[ToolNameEnum.EDIT_FILE]: ({ message }, messageIdx: number) => {
		return null
	},
	[ToolNameEnum.EXEC_COMMAND]: ({ message }, messageIdx: number, containerId?: string, threadId?: string) => {
		return <ExecuteCommandTool
					toolCall={message.toolCall}
					toolCallResult={message.toolCallResult }
					messageIdx={messageIdx}
					containerId={containerId!}
					threadId={threadId!}
			 	/>;
	},
	[ToolNameEnum.DELETE_FILE]: ({ message }, messageIdx: number) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.DELETE_FILE]
		return <ToolResult
			icon="codicon codicon-trash"
			title={`Delete file ${params.target_file}`}
			toolCallResult={message.toolCallResult}
		></ToolResult>
	},
	[ToolNameEnum.CALL_TRACE_QUERY]: ({ message }, messageIdx: number) => {
		const params = message.toolCall.params as ToolCallParamsType[ToolNameEnum.CALL_TRACE_QUERY]
		return <ToolResult
			icon="codicon codicon-search"
			title={`Called Trace for "${params.function}"`}
			toolCallResult={message.toolCallResult}
		></ToolResult>
	}
}


export const defaultToolComponent = ( message:ToolMessage , messageIdx: number, isExecuting: boolean) => {
	const accessor = useAccessor();
	const chatThreadsService = accessor.get('IChatThreadService');
	const toolsService = accessor.get('IToolsService');
	const containerId = chatThreadsService.getCurrentContainerId();

	const onAbort = useCallback(() => {
		const threadId = chatThreadsService.getCurrentThreadId(containerId);
		toolsService.cancelExecTool(threadId);
	}, [chatThreadsService, containerId]);
	const toolCallReturn = message.toolCallResult.content

	return (
		<div className="text-codeseek-fg-2 px-2 py-1">
			<div className="px-2 py-0.5 mb-1 bg-codeseek-bg-1 rounded">
				<div className="flex items-center justify-between">
					<div className="flex-grow text-left">{`调用工具${message.toolCall.name}:`}</div>
					{!message.toolCallResult && isExecuting && <ButtonStop
						className="ml-auto mr-2 mt-0.5 text-codeseek-fg-3"
						onClick={onAbort} />
					}
				</div>
				<BlockCode
					initValue={JSON.stringify(message.toolCall.params, null, 4)}
					language={"json"}
					tokenId={messageIdx + 'json'} />

			</div>
			{ toolCallReturn && <div className="px-2 py-0.5 mb-1 bg-codeseek-bg-1 rounded">
						工具执行结果为：
						<BlockCode
					initValue={toolCallReturn}
					language={"shell"}
					tokenId={messageIdx + 'shell'} />

				</div>
			}
		</div>);
}

export const ExecuteCommandTool = ({ toolCall, toolCallResult, messageIdx, containerId, threadId }: { toolCall: ToolCallType, toolCallResult: ToolCallResultType, messageIdx: number, containerId: string, threadId: string }) => {
	const accessor = useAccessor()
	const chatThreadsService = accessor.get('IChatThreadService')
	const toolsService = accessor.get('IToolsService')

	const params = toolCall.params as ToolCallParamsType[ToolNameEnum.EXEC_COMMAND]
	const toolCallReturn = toolCallResult.toolCallReturn as ToolCallReturnType[ToolNameEnum.EXEC_COMMAND]
	const initialCmd = `${params.workdir ? `cd ${params.workdir} && ` : ''} ${params.command}`
	const [currentCmd, setCurrentCmd] = useState(initialCmd)

	const onStop = useCallback(() => {
		const threadId = chatThreadsService.getCurrentThreadId(containerId)
		toolsService.cancelExecTool(threadId)
	},[chatThreadsService, containerId])
	const [mode, setMode] = useState<'display' | 'edit'>('display')
	const [isHovered, setIsHovered] = useState(false)
	const [textAreaRefState, setTextAreaRef] = useState<HTMLTextAreaElement | null>(null)
	const textAreaFnsRef = useRef<TextAreaFns | null>(null)
	const _justEnabledEdit = useRef(false)

	useEffect(() => {
		if (mode === 'edit' && textAreaRefState && _justEnabledEdit.current) {
			if (textAreaFnsRef.current) {
				textAreaFnsRef.current.setValue(currentCmd)
			}
			textAreaRefState.focus();
			_justEnabledEdit.current = false
		}
	}, [mode, textAreaRefState, currentCmd])

	const onRun = useCallback(() => {
		const text = textAreaFnsRef.current?.getValue() || currentCmd;
		const message = {
			type: "tool",
			response: AskReponseType.yesButtonClicked,
			text: text,
		}
		chatThreadsService.setAskResponse(threadId, message)
	}, [chatThreadsService, threadId, toolCall, currentCmd])

	const onCancel = useCallback(() => {
		const message = {
			type: "tool",
			response: AskReponseType.noButtonClicked,
		}
		chatThreadsService.setAskResponse(threadId, message)
	}, [chatThreadsService, threadId])

	const EditSymbol = mode === 'display' ? Pencil : X

	const onOpenEdit = () => {
		_justEnabledEdit.current = true
		setMode('edit')
	}

	const onCloseEdit = () => {
		if (textAreaFnsRef.current) {
			const newText = textAreaFnsRef.current.getValue()
			setCurrentCmd(newText)
		}
		setIsHovered(false)
		setMode('display')
	}
	let cmdTextArea;
	if (mode === 'display') {
		cmdTextArea = <textarea className="bg-codeseek-bg-2 text-codeseek-fg-1 pl-8 text-root w-full" value={currentCmd} readOnly style={{ resize: 'none' }} />
	} else {
		cmdTextArea = <textarea
			className="bg-codeseek-bg-2 text-codeseek-fg-1 pl-8 text-root w-full"
			defaultValue={currentCmd}
			ref={(ref) => {
				setTextAreaRef(ref);
				if (ref && textAreaFnsRef.current === null) {
					textAreaFnsRef.current = {
						setValue: (value) => {
							ref.value = value;
						},
						getValue: () => ref.value,
						enable: () => {
							ref.disabled = false;
						},
						disable: () => {
							ref.disabled = true;
						}
					};
				}
			}}
			onChange={(e) => {
				if (textAreaFnsRef.current) {
					textAreaFnsRef.current.setValue(e.target.value);
				}
			}}
			style={{ resize: 'none' }}
		/>
	}

	return (
		<div className='px-2 py-2'>
		<div className="px-2 pt-1 pb-2 text-codeseek-fg-2 bg-codeseek-bg-1 rounded-md">
			<div className="text-left pb-1">在终端执行以下命令：</div>
			<div className="bg-codeseek-bg-2 rounded border border-codeseek-border-3 rounded-md">
				<div className="flex-col items-center justify-between">
					<div className="py-1 flex items-center gap-1 relative flex-grow"
						onMouseEnter={() => setIsHovered(true)}
						onMouseLeave={() => setIsHovered(false)}
					>
						<div className="codicon codicon-terminal flex select-file-icon absolute left-2 top-3 transform -translate-y-1/2 z-10" />
						{cmdTextArea}
						{toolCallResult.status === ToolCallStatus.idle && <EditSymbol
							size={18}
							className={`
								absolute -top-1 right-0
								translate-x-0 -translate-y-0
								cursor-pointer z-1
								p-[2px]
								bg-codeseek-bg-1 border border-codeseek-border-1 rounded-md
								transition-opacity duration-200 ease-in-out
								${isHovered ? 'opacity-100' : 'opacity-0'}
							`}
							onClick={() => {
								if (mode === 'display') {
									onOpenEdit()
								} else if (mode === 'edit') {
									onCloseEdit()
								}
							}}
						/>}
					</div>
				</div>
				{mode === 'display' && <div className="flex justify-end pb-1">
					{ toolCallResult.status === ToolCallStatus.executing && <div
						className="px-3 bg-transparent-80 text-codeseek-fg-3 rounded hover:text-codeseek-fg-1 hover:bg-transparent-50 cursor-pointer"
						onClick={onStop}
					>
						Stop
					</div>}
					{ toolCallResult.status === ToolCallStatus.idle && <div
						className="px-3 bg-transparent-80 text-codeseek-fg-3 rounded hover:text-codeseek-fg-1 hover:bg-transparent-50 cursor-pointer"
						onClick={onCancel}
					>
						Cancel
					</div>}
					{ toolCallResult.status === ToolCallStatus.idle && <div
						className="px-3 bg-transparent-80 text-codeseek-fg-3 rounded hover:text-codeseek-fg-1 hover:bg-transparent-50 cursor-pointer"
						onClick={onRun}
					>
						Run
					</div>}
				</div>
				}
			</div>
		</div>
		{
			(toolCallResult.status === ToolCallStatus.success && toolCallReturn?.output) && <div className="px-2 py-0.5 mb-1 bg-codeseek-bg-1 rounded">
					命令执行结果为：
					<BlockCode
						initValue={toolCallReturn.output}
						language={"shell"}
						tokenId={messageIdx + 'shell'}
					/>
			</div>
		}
	</div>)
}
