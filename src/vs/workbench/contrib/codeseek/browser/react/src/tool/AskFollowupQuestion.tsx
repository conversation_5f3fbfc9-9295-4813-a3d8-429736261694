import React, { useCallback, useMemo, useState } from "react"
import { Edit } from "lucide-react"
import { CodeseekButton } from '../util/inputs'
import { AskFollowupQuestionResultType, ToolCallParamsType, ToolNameEnum } from '../../../../common/toolsServiceTypes'
import { ChatMarkdownRender } from '../markdown/ChatMarkdownRender'
import { useAccessor } from '../util/services'

interface FollowUpSuggestProps {
	className?: string,
	suggestions?: string[],
	selectedIndex?: number,
	onSuggestionClick?: ClickCallBackProps
	ts?: number,
}

export const FollowupSuggestiton: React.FC<FollowUpSuggestProps> = ({ className ='', suggestions = [], selectedIndex = null, onSuggestionClick, ts = 1 }) => {
	const handleSuggestionClick = useCallback(
		(suggestion: string, event: React.MouseEvent, index: number) => {
			if (selectedIndex !== null) return; // 已有选项被选中，不再响应点击
			onSuggestionClick?.(suggestion, index, event);
		},
		[onSuggestionClick],
	)
	const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);


	// Don't render if there are no suggestions or no click handler.
	if (!suggestions?.length) {
		return null
	}

	const defaultColor = 'color-mix(in srgb, var(--vscode-list-inactiveSelectionBackground) 50%, transparent)';
	const activeColor = 'color-mix(in srgb, var(--vscode-list-inactiveSelectionBackground) 80%, blue)';
	const selectedColor = 'color-mix(in srgb, var(--vscode-list-inactiveSelectionBackground) 80%, blue)';

	return (
		<ul className="border border-codeseek-border-3 rounded-md overflow-y-auto overflow-x-hidden px-2 py-1">
			{suggestions.map((suggestion, index) => (
				<li className={`mt-1 py-1 rounded-sm transition-colors duration-150 ${selectedIndex === null ? 'cursor-pointer' : 'cursor-default'}`}
					style={{
						backgroundColor: selectedIndex === index ? selectedColor : (hoveredIndex === index && selectedIndex === null ? activeColor : defaultColor)
					}}
					onClick={(event) => {
						event.preventDefault();
						handleSuggestionClick(suggestion, event, index);
					}}
					onMouseEnter={() => selectedIndex === null && setHoveredIndex(index)}
					onMouseLeave={() => selectedIndex === null && setHoveredIndex(null)}
					key={index}
					aria-label={suggestion}>
					<div className={`flex items-center w-full px-2`}>
						<div className="">{suggestion}</div>
					</div>
				</li>
			))}
		</ul>
	)
}

export type ClickCallBackProps = (answer: string, selectedIndex: number, event?: React.MouseEvent) => void
type AskFollowupQuestionProps = {
	params: ToolCallParamsType[ToolNameEnum.ASK_FOLLOWUP_QUESTION],
	result: AskFollowupQuestionResultType | null,
	onSuggestionClick?: ClickCallBackProps
}

export const AskFollowupQuestion:React.FC<AskFollowupQuestionProps> = ({ params, result, onSuggestionClick }) => {
	const [icon, title] = useMemo(() => {
		return [
			<span
				className="codicon codicon-question text-codeseek-fg-1 mb-[-1.5px]"
			/>,
			<span className="text-codeseek-fg-1 font-bold">{"Flow has a question:"}</span>,
		]
	}, [])
	return (
		<div className='pl-4 text-codeseek-fg-1 px-2'>
			{title && (
				<div className="flex items-center gap-1 break-word">
					{icon}
					{title}
				</div>
			)}
			<div className={`flex flex-col gap-1 bg-codeseek-bg-1 mt-1 px-2 py-1 ${result?.content !== null ? 'rounded-t-md' : 'rounded-md'}`}>
				<div className="leading-relaxed">{params.question}</div>
				<div className="">
					<FollowupSuggestiton
						key="suggestion"
						suggestions={params.suggest}
						selectedIndex={result?.selectedIndex}
						onSuggestionClick={onSuggestionClick}
						ts={1}
					/>
				</div>
			</div>
 			{result && <UserFeedBack content={result.content}/>}

		</div>
	)
}

const UserFeedBack = ({content}:{content:string}) => {
	return  (
		<div className="flex items-center w-full relative group mb-2 pl-2 text-codeseek-fg-1 bg-codeseek-bg-1 rounded-b-md">
			<span className="font-bold">{"User:"}</span>
			<CodeseekButton
				// variant="outline"
				className="text-left whitespace-normal break-words w-full h-auto py-1 justify-start"
				disabled
				aria-label={content}>
				<div>{content}</div>
			</CodeseekButton>
		</div>
	)
}
