import { Disposable } from '../../../../../base/common/lifecycle.js';
import { Emitter, Event } from '../../../../../base/common/event.js';
import { LLMChatMessage } from '../../common/llmMessageTypes.js';
import { ChatMessage } from '../chatThreadType.js';
import { InternalToolInfo, ToolCallParamsType, ToolCallResultType, ToolCallType, ToolNameEnum } from '../../common/toolsServiceTypes.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { IChatThreadService } from '../chatThreadService.js';
import { tripleTick } from '../../common/prompt/tags.js';
import { filenameToVscodeLanguage } from '../../common/helpers/detectLanguage.js';

export const MAX_MESSAGES_SENT = 25;

export type ExtractToolResult = {
	status: 'success' | 'error';
	toolCalls: ToolCallType[];
	error?: string;
}

export class ExceededMaxLoopCountError extends Error {
	constructor(message: string) {
		super(message);
		this.name = 'ExceededMaxLoopCountError';
	}
}

export class BaseAgent extends Disposable {

	readonly _onDidAbort = new Emitter<string>();
	readonly onDidAbort: Event<string> = this._onDidAbort.event;

	AGENT_END_TAG = "<final_answer>";
	NO_SUPPORT_FUNCTION_TAG = "功能不支持";

	FORMAT_ERROR_MESSAGE = `你的回答格式不正确, 提示：任务结束使用 ${this.AGENT_END_TAG} 标签， 工具调用需使用'工具集'中的给定的标签，请重新回答。`;
	TOOL_FORMAT_ERROR_MESSAGE = `你调用的工具不在工具集中，换调用工具或者结束任务。`;

	chatThreadService: IChatThreadService;

	constructor(
		@IInstantiationService readonly instantiationService: IInstantiationService,
	) {
		super();
		this.chatThreadService = instantiationService.invokeFunction(accessor => accessor.get(IChatThreadService));
	}

	public async handleText(oldText: string, NewText: string, containerId: string, threadId: string, isHandleCode = false, isAbortedFn?: () => boolean) {
		let processedLength = 0;
		let streamInterval = 30;
		let chunkSize = 3;
		let accumulatedText = oldText;

		if (isHandleCode) {
			streamInterval = 10;
			chunkSize = 5;
		}

		while (processedLength < NewText.length) {
			if (isAbortedFn && isAbortedFn()) {
				this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: undefined });
				return;
			}

			const endIndex = Math.min(processedLength + chunkSize, NewText.length);
			const newChunk = NewText.substring(processedLength, endIndex);
			processedLength = endIndex;
			accumulatedText += newChunk;
			this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: accumulatedText });
			await new Promise(resolve => setTimeout(resolve, streamInterval));
		}

		this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: undefined });
	}

	public extractFinalAnswer(text: string): string {
		const finalAnswerRegex = /<final_answer>([\s\S]*?)<\/final_answer>/gi;
		const finalAnswerMatch = finalAnswerRegex.exec(text);
		return finalAnswerMatch ? finalAnswerMatch[1].trim() : '';
	}

	public extractThinking(text: string): string {
		const thinkingRegex = /<thinking>([\s\S]*?)<\/thinking>/gi;
		const thinkingMatch = thinkingRegex.exec(text);
		return thinkingMatch ? thinkingMatch[1].trim() : '';
	}

	public extractSummary(text: string): string {
		const summaryRegex = /<summary>([\s\S]*?)<\/summary>/gi;
		const summaryMatch = summaryRegex.exec(text);
		return summaryMatch ? summaryMatch[1].trim() : '';
	}

	public extractGoal(text: string): string {
		const goalRegex = /<goal>([\s\S]*?)<\/goal>/gi;
		const goalMatch = goalRegex.exec(text);
		return goalMatch ? goalMatch[1].trim() : '';
	}

	public extractContent(text: string): string {
		const contentRegex = /<content>([\s\S]*?)<\/content>/gi;
		const contentMatch = contentRegex.exec(text);
		return contentMatch ? contentMatch[1].trim() : '';
	}

	public extractReference(text: string): string {
		const referenceRegex = /<reference>([\s\S]*?)<\/reference>/gi;
		const referenceMatch = referenceRegex.exec(text);
		return referenceMatch ? referenceMatch[1].trim() : '';
	}

	public extractNeedActor(text: string): string {
		const needActorRegex = /<need_actor>([\s\S]*?)<\/need_actor>/gi;
		const needActorMatch = needActorRegex.exec(text);
		return needActorMatch ? needActorMatch[1].trim() : '';
	}

	public extractEditFileTool(toolCalls: ToolCallType[]): string {
		let codeblocks = [];
		for (const toolCall of toolCalls) {
			if (toolCall.name !== ToolNameEnum.EDIT_FILE) {
				continue;
			}
			const path = (toolCall.params as ToolCallParamsType[ToolNameEnum.EDIT_FILE]).path;
			let changeStr = (toolCall.params as ToolCallParamsType[ToolNameEnum.EDIT_FILE]).content;
			const language = filenameToVscodeLanguage(path);
			changeStr = changeStr.replace(`${tripleTick[0]}${language}`, '').replace(tripleTick[1], '').trim();

			codeblocks.push(`
${tripleTick[0]}${language}:${path}
${changeStr}
${tripleTick[1]}
`);
		}
		return codeblocks.join('\n\n');
	}

	public joinToolResult(toolResult: ToolCallResultType[]) {
		return toolResult.filter(tool => tool.content != '').map(tool => tool.content).join('\n\n');
	}

	public toLLMChatMessage(c: ChatMessage): LLMChatMessage {
		if (c.role === 'system' || c.role === 'user') {
			return { role: c.role, content: c.content === '' ? '(empty message)' : c.content ?? '(empty message)' };
		}
		else if (c.role === 'assistant')
			return { role: c.role, content: c.content === '' ? '(empty message)' : c.content ?? '(empty message)' };
		else if (c.role === 'tool')
			return { role: 'user', content: c.content === '' ? '(empty output)' : c.content ?? '(empty output)' };
		else {
			throw 1;
		}
	};

	public extractShowContent(text: string): string {
		const referenceRegex = /<show_content>([\s\S]*?)<\/show_content>/gi;
		const referenceMatch = referenceRegex.exec(text);
		return referenceMatch ? referenceMatch[1].trim() : '';
	}

	public extractTool(text: string, validateToolCalls: InternalToolInfo[]): ExtractToolResult {
		let toolCalls: ToolCallType[] = [];
		let extractionError: string | undefined;

		try {
			toolCalls = this.extractTool_(text, validateToolCalls);
		} catch (e) {
			extractionError = `Error extracting tools from response: ${e}`;
		}
		return {
			status: extractionError ? 'error' : 'success',
			toolCalls,
			error: extractionError
		};
	}

	private extractTool_(text: string, validateToolCalls: InternalToolInfo[]): ToolCallType[] {
		const toolCalls: ToolCallType[] = [];

		// 创建所有工具名称的正则表达式模式
		const toolNames = validateToolCalls.map(tool => tool.name);
		const toolNamesPattern = toolNames.join('|');

		// 匹配工具调用的正则表达式：<tool_name>...</tool_name>
		const toolCallRegex = new RegExp(`<(${toolNamesPattern})>([\\s\\S]*?)<\\/\\1>`, 'gi');

		let match;
		while ((match = toolCallRegex.exec(text)) !== null) {
			const toolName = match[1] as ToolNameEnum;
			const toolContent = match[2];

			// 提取参数
			const params = this.extractParameters(toolContent);
			// 根据工具名称映射参数到正确的结构
			const mappedParams = this.mapToolParameters(toolName, params, validateToolCalls);

			toolCalls.push({
				name: toolName,
				params: mappedParams,
				id: `${toolName}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
			});
		}

		return toolCalls;
	};

	// 提取XML标签内的参数
	private extractParameters = (content: string): Record<string, string | string[]> => {
		const params: Record<string, string | string[]> = {};

		// 匹配参数标签：<param_name>value</param_name>
		const paramRegex = /<([^>]+)>([\s\S]*?)<\/\1>/g;

		let match;
		const paramMap = new Map<string, string[]>();
		while ((match = paramRegex.exec(content)) !== null) {
			const paramName = match[1].trim();
			const paramValue = match[2].trim();
			if (paramMap.has(paramName)) {
				paramMap.get(paramName)!.push(paramValue);
			} else {
				paramMap.set(paramName, [paramValue]);
			}
		}
		for (const [key, value] of paramMap) {
			if (value.length > 1) {
				params[key] = value;
			} else {
				params[key] = value[0];
			}
		}
		return params;
	};

	// 动态参数类型转换辅助
	private convertType(value: any, type: string) {
		switch (type) {
			case 'string':
				return Array.isArray(value) ? value[0] ?? '' : value ?? '';
			case 'number': {
				const v = Array.isArray(value) ? value[0] : value;
				const n = Number(v);
				return isNaN(n) ? 0 : n;
			}
			case 'boolean': {
				const v = Array.isArray(value) ? value[0] : value;
				if (typeof v === 'boolean') return v;
				if (typeof v === 'string') return v === 'true';
				return Boolean(v);
			}
			case 'string[]':
				if (Array.isArray(value)) return value;
				if (typeof value === 'string') return [value];
				return [];
			default:
				return value;
		}
	}

	// 动态参数映射与校验
	private mapToolParameters = (toolName: ToolNameEnum, params: Record<string, string | string[]>, validateToolCalls: InternalToolInfo[]): any => {
		const toolDef = validateToolCalls.find(tool => tool.name === toolName);
		if (!toolDef) return params;

		let processedParams = { ...params };

		const result: Record<string, any> = {};
		const missingRequired: string[] = [];

		// 处理所有参数定义
		for (const [paramName, paramInfo] of Object.entries(toolDef.params)) {
			const type = paramInfo.type;
			let value = processedParams[paramName];

			// 兼容常见别名（如 path/uri 等）
			if (value === undefined) {
				// 这里可根据实际需要扩展别名映射
				const aliasMap: Record<string, string[]> = {
					path: ['uri', 'path'],
					relative_workspace_path: ['uri', 'path', 'relative_workspace_path'],
				};
				const aliases = aliasMap[paramName];
				if (aliases) {
					for (const alias of aliases) {
						if (processedParams[alias] !== undefined) {
							value = processedParams[alias];
							break;
						}
					}
				}
			}

			// 类型转换
			result[paramName] = this.convertType(value, type);

			// 校验必需参数（只校验参数是否存在，不校验值）
			if (toolDef.required.includes(paramName)) {
				if (!(paramName in processedParams)) {
					missingRequired.push(paramName);
				}
			}
		}

		// 如果缺少必需参数，抛出错误
		if (missingRequired.length > 0) {
			throw new Error(`工具 ${toolName} 缺少必需参数: ${missingRequired.join(', ')}。模型返回的数据不完整。`);
		}

		return result;
	};
}
