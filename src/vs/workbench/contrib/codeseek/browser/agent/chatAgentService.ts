import { createDecorator, IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { userMessageOpts, ChatMessage } from '../chatThreadType.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { FeatureNames } from '../../common/codeseekSettingsTypes.js';
import { ILLMMessageService } from '../../common/llmMessageService.js';
import { summaryUserMessage, summarySystemMessage } from '../../common/prompt/prompts.js';
import { IWorkspaceContextService } from '../../../../../platform/workspace/common/workspace.js';
import { IEditorService } from '../../../../../workbench/services/editor/common/editorService.js';
import { EditorsOrder } from '../../../../../workbench/common/editor.js';
import { getRelativePath } from '../../common/helpers/path.js';
import { InstantiationType, registerSingleton } from '../../../../../platform/instantiation/common/extensions.js';
import { IConvertToLLMMessageService, Prompt } from '../convertToLLMMessageService.js';
import { SendLLMType } from '../../common/llmMessageTypes.js';
import { generateUuid } from '../../../../../base/common/uuid.js';
import { PlanType } from '../../common/remoteAgentServiceType.js';
import { ToolCallStatus, ToolCallResultType, ToolNameEnum, codeseekTools, InternalToolInfo } from '../../common/toolsServiceTypes.js';
import { IToolsService } from '../../common/toolsService.js';
import { BaseAgent, ExceededMaxLoopCountError, MAX_MESSAGES_SENT } from './baseAgent.js';
import { ChatMode } from '../../common/codeseekSettingsService.js';
import { removeCloseThinkTag } from '../../common/helpers/common.js';

export type ContextEnhancementResult = {
	status: 'success' | 'failed';
	content: string;
	reference: string;
	needActor: boolean;
	error?: string;
}

export type SendLLMMessageResult = {
	status: 'success' | 'failed';
	content: string;
	reference: string;
	needActor: boolean;
}

export type ChatAgentCreatePlanResult = {
	status: 'success' | 'failed';
	plan: PlanType | undefined;
}

export type ChatAgentState = {
	[threadId: string]: ChatMessage[];
}

export interface IChatAgentService {
	readonly _serviceBrand: undefined;

	createPlan(containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<ChatAgentCreatePlanResult>;
	abort(cancelToken: string): void;
	contextEnhancement(containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<ContextEnhancementResult>;

}

export const IChatAgentService = createDecorator<IChatAgentService>('codeseekChatAgentService');


export class ChatAgentService extends BaseAgent implements IChatAgentService {
	readonly _serviceBrand: undefined;
	private state: ChatAgentState = {};
	private isAborted = false;
	private currentContainerId?: string;
	private currentThreadId?: string;

	private commonInternalTools: ToolNameEnum[] = [
		ToolNameEnum.READ_FILE,
		ToolNameEnum.LIST_DIR,
		ToolNameEnum.FILE_SEARCH,
		ToolNameEnum.GREP_SEARCH,
		ToolNameEnum.CODEBASE_SEARCH,
		ToolNameEnum.CALL_TRACE_QUERY,
		ToolNameEnum.ASK_FOLLOWUP_QUESTION
	];

	private askInternalTools: ToolNameEnum[] = [
		...this.commonInternalTools,
	];

	private agentInternalTools: ToolNameEnum[] = [
		...this.commonInternalTools,
	];

	constructor(
		@ILLMMessageService private readonly llmMessageService: ILLMMessageService,
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
		@IEditorService private readonly editorService: IEditorService,
		@IToolsService private readonly toolsService: IToolsService,
		@IConvertToLLMMessageService private readonly convertToLLMMessageService: IConvertToLLMMessageService,
		@IInstantiationService override readonly instantiationService: IInstantiationService,
	) {
		super(instantiationService);
	}

	public async createPlan(containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<ChatAgentCreatePlanResult> {
		this.logger.info('[Agent] ChatAgentService create plan');
		const id = generateUuid();
		if (userMessageOpts.from === 'Fix') {
			return {
				status: 'success',
				plan: {
					id,
					params: {
						task: userMessageOpts.userMessage,
						taskContext: '',
						taskPlan: [{ content: userMessageOpts.userMessage, instruction: '' }],
						dependencies: new Map(),
					}
				}
			}
		} else {
			let contextEnhancement = await this.contextEnhancement(containerId, threadId, userMessageOpts);
			if (contextEnhancement.status === 'failed') {
				return { status: 'failed', plan: undefined };
			}
			if (contextEnhancement.needActor) {
				const taskContext = [contextEnhancement.content, contextEnhancement.reference].join('\n\n');
				return {
					status: 'success',
					plan: {
						id,
						params: {
							task: userMessageOpts.userMessage,
							taskContext: taskContext,
							taskPlan: [{ content: userMessageOpts.userMessage, instruction: '' }],
							dependencies: new Map(),
						}
					}
				};
			} else {
				this.logger.info('[Agent] ChatAgentService believe that the task does not require a design plan');
				return {
					status: 'success',
					plan: undefined
				}
			}
		}
	}

	async contextEnhancement(containerId: string, threadId: string, userMessageOpts: userMessageOpts): Promise<ContextEnhancementResult> {
		// Reset abort state for new execution
		this.isAborted = false;

		this.logger.info('[Agent] ChatAgentService context enhancement');
		const chatHistory = await this.chatHistorySummary(containerId, threadId, userMessageOpts.userMessage, userMessageOpts.workspaceInfo?.userId ?? '');
		this.logger.info('the summary chat history: ', chatHistory);
		const internalTools = userMessageOpts.chatMode === ChatMode.Agent ? this.agentInternalTools : this.askInternalTools
		const validTools: InternalToolInfo[] = internalTools.map(tool => codeseekTools[tool as ToolNameEnum]);
		const prompt = await this.convertToLLMMessageService.prepareLLMChatMessages({
			messagesType: 'chatAgentMessages',
			containerId,
			threadId,
			userMessageOpts: userMessageOpts,
			chatHistory,
			internalTools: validTools,
		});

		const result = await this.sendLLMMessage(prompt, containerId, threadId, userMessageOpts, validTools);
		if (result.status === 'failed') {
			return { status: 'failed', content: '', reference: '', needActor: false };
		}
		return { status: "success", content: result.content, reference: result.reference, needActor: result.needActor };
	}

	async sendLLMMessage(prompt: Prompt, containerId: string, threadId: string, userMessageOpts: userMessageOpts, validTools: InternalToolInfo[]): Promise<SendLLMMessageResult> {
		// Store current execution context for abort
		this.currentContainerId = containerId;
		this.currentThreadId = threadId;

		let shouldSendAnotherMessage = true;
		let nAttempts = 0;
		let result: SendLLMMessageResult = { status: 'success', content: '', reference: '', needActor: true };
		this.state[threadId] = [
			...prompt.userPrompts.map(p => ({ role: 'user', content: p } as ChatMessage)),
		];
		const chatId = this.chatThreadService.getCurrentThreadChatId(threadId);

		while (shouldSendAnotherMessage && !this.isAborted) {
			// Check for abort status at the beginning of each loop iteration
			if (this.isAborted) {
				this.logger.info(`[Agent] ChatAgentService sendLLMMessage aborted`);
				return { status: 'failed', content: '', reference: '', needActor: false };
			}

			shouldSendAnotherMessage = false;

			if (nAttempts > MAX_MESSAGES_SENT) {
				this.logger.info('[Agent] ChatAgentService tool exceeded max call count');
				result = { status: 'failed', content: '', reference: '', needActor: true };
				throw new ExceededMaxLoopCountError('Tool exceeded max call count');
			}
			nAttempts += 1;
			let res_: () => void;
			const awaitable = new Promise<void>((res, rej) => { res_ = res; });
			let messages = this.state[threadId].map(m => (this.toLLMChatMessage(m)));
			const chatMessages: SendLLMType = {
				messagesType: 'chatMessages',
				messages: [
					{ role: 'system', content: prompt.systemPrompt },
					...messages,
				],
			}
			let toolCallResults: ToolCallResultType[] = [];
			const cancelToken = this.llmMessageService.sendLLMMessage({
				containerId,
				userId: userMessageOpts.workspaceInfo?.userId ?? '',
				useProviderFor: FeatureNames.CtrlL,
				...chatMessages,
				onText: ({ fullText }) => {
					// this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: fullText });
				},
				onFinalMessage: async ({ fullText, toolCalls }) => {
					fullText = removeCloseThinkTag(fullText);
					this.logger.info(`[Agent] ChatAgentService fullText: ${fullText}, toolCalls: ${JSON.stringify(toolCalls)}`);

					const extractToolResult = this.extractTool(fullText, validTools);
					this.logger.info(`[Agent] ChatAgentService extractToolResult: ${JSON.stringify(extractToolResult)}`);
					if (extractToolResult.status === 'error') {
						this.state[threadId].push({ role: 'assistant', content: fullText, displayContent: fullText, chatId });
						this.logger.warn('[Agent] ChatAgentService extract tool format error');
						this.state[threadId].push({ role: 'user', content: extractToolResult.error, displayContent: extractToolResult.error, state: {} } as ChatMessage);
						shouldSendAnotherMessage = true;
					} else {
						toolCalls?.push(...extractToolResult.toolCalls);
						if ((toolCalls?.length ?? 0) === 0 && fullText.includes(this.AGENT_END_TAG)) {
							result.content = this.extractContent(fullText);
							result.reference = this.extractReference(fullText);
							result.needActor = this.extractNeedActor(fullText).toLocaleLowerCase() === 'true';
						}

						let fullText_: string[] = [];
						const thinking = this.extractThinking(fullText);
						if (thinking) {
							fullText_.push(thinking);
						}
						if (fullText_.length > 0 && result.needActor) {
							await this.handleText('', fullText_.join('\n\n'), containerId, threadId, false, () => this.isAborted);
							this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText_.join('\n\n'), displayContent: fullText_.join('\n\n'), chatId });
						}
						this.state[threadId].push({ role: 'assistant', content: fullText, displayContent: fullText, chatId });

						if ((toolCalls?.length ?? 0) === 0) {
							if (fullText.includes(this.AGENT_END_TAG)) {
								this.logger.info('[Agent] ChatAgentService end of agent');
								this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: undefined, isLoading: false });
							} else {
								this.logger.warn('[Agent] ChatAgentService format error');
								this.state[threadId].push({ role: 'user', content: this.FORMAT_ERROR_MESSAGE, displayContent: this.FORMAT_ERROR_MESSAGE, state: {} } as ChatMessage);
								shouldSendAnotherMessage = true;
							}
							if (!result.needActor) {
								let finalAnswer = result.content;
								if (!finalAnswer) {
									finalAnswer = this.extractFinalAnswer(fullText);
								}
								if (finalAnswer === this.NO_SUPPORT_FUNCTION_TAG) {
									this.chatThreadService.addUserMessageAndStreamResponse({ containerId, userMessageOpts: userMessageOpts, isFromAgent: true });
								} else {
									await this.handleText('', finalAnswer, containerId, threadId, false, () => this.isAborted);
									this.chatThreadService.addMessageToThread(containerId, threadId, {
										role: 'assistant',
										content: finalAnswer,
										displayContent: finalAnswer,
										chatId,
									}, true);
								}
							}
						}
						else {
							this.logger.info('[Agent] ChatAgentService tool call');
							const isCallFormatOK = toolCalls?.every(toolCall => validTools.some(t => t.name === toolCall.name));
							if (!isCallFormatOK) {
								this.state[threadId].push({ role: 'user', content: this.TOOL_FORMAT_ERROR_MESSAGE, displayContent: this.TOOL_FORMAT_ERROR_MESSAGE, state: {} } as ChatMessage);
								shouldSendAnotherMessage = true;
							} else {
								for (const toolCall of toolCalls ?? []) {
									// Check for abort status before each tool call
									if (this.isAborted) {
										this.logger.info(`[Agent] ChatAgentService tool call aborted`);
										result = { status: 'failed', content: '', reference: '', needActor: false };
										res_();
										return;
									}

									this.chatThreadService.setStreamState(containerId, threadId, {
										tool: {
											toolCall,
											toolCallResult: {
												status: ToolCallStatus.idle,
												name: toolCall.name as ToolNameEnum,
												content: '',
												error: undefined,
												toolCallReturn: null,
											},
										}
									});
									const toolCallResult: ToolCallResultType = await this.toolsService.executeTool(containerId, threadId, chatId, toolCall, userMessageOpts);
									this.logger.info(`[Agent] ChatAgentService toolCallResult: ${JSON.stringify(toolCallResult)}`);
									toolCallResults.push(toolCallResult);
									this.chatThreadService.updateToolCall(containerId, threadId, {
										toolCallResult
									});
									this.chatThreadService.addMessageToThread(containerId, threadId, {
										role: 'tool',
										content: toolCallResult.status === ToolCallStatus.success ? toolCallResult.content : toolCallResult.error,
										toolCall: this.chatThreadService.streamState[containerId]?.[threadId]?.tool?.toolCall || toolCall,
										toolCallResult: toolCallResult,
										chatId,
									});
									shouldSendAnotherMessage = true;
									this.chatThreadService.setStreamState(containerId, threadId, { tool: undefined });
									await new Promise(resolve => setTimeout(resolve, 1000));
								}
								const content = this.joinToolResult(toolCallResults);
								this.state[threadId].push({ role: 'tool', content: content } as ChatMessage);
							}
						}
					}
					res_();
				},
				onError: (error) => {
					this.logger.error('[Agent] ChatAgentService error: ', error);
					this.chatThreadService.finishStreamingTextMessage(containerId, threadId, '', error);
					result = { status: 'failed', content: '', reference: '', needActor: false };
					res_();
				},
				logging: { loggingName: 'ChatAgentService' }
			});
			if (cancelToken === null) break;
			this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: cancelToken, isLoading: true });
			await awaitable;
		}
		delete this.state[threadId];
		return result;
	}

	public abort(cancelToken: string): void {
		this.logger.info(`[Agent] ChatAgentService aborting requestId: ${cancelToken}`);
		this.isAborted = true;

		// Immediately clear stream state if we have execution context
		if (this.currentContainerId && this.currentThreadId) {
			this.chatThreadService.chatEnd(this.currentContainerId, this.currentThreadId);
		}

		this.llmMessageService.abort(cancelToken);
	}

	async chatHistorySummary(containerId: string, threadId: string, userInput: string, userId: string): Promise<string> {
		this.logger.info('[Agent] ChatAgentService chat history summary');
		const previousMessages = this.formatChatHistory(containerId);
		if (!previousMessages) {
			return '';
		}
		const userMessage = summaryUserMessage(previousMessages, userInput);
		let res_: () => void;
		const awaitable = new Promise<void>((res, rej) => { res_ = res; });
		let summary = '';
		const cancelToken = this.llmMessageService.sendLLMMessage({
			containerId,
			userId,
			useProviderFor: FeatureNames.CtrlL,
			messagesType: 'chatMessages',
			messages: [{ role: 'system', content: summarySystemMessage }, { role: 'user', content: userMessage }],
			onText: () => { },
			onFinalMessage: (params) => {
				this.logger.info('chatHistorySummary, got result: ', params.fullText);
				summary = params.fullText;
				res_();
			},
			onError: (error) => {
				this.chatThreadService.finishStreamingTextMessage(containerId, threadId, '', error);
				this.logger.error('chatHistorySummary, error: ', error.message, error.fullError);
				res_();
			},
			logging: { loggingName: 'ChatAgentService' }
		});
		if (!cancelToken) return '';
		this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: cancelToken, isLoading: true });
		await awaitable;
		this.logger.info('[Agent] ChatAgentService chat history summary done');
		this.logger.info('[Agent] the summary: ', summary);
		return summary;
	}

	/**
	 * 将 ChatMessage 数组格式化为可读的历史记录字符串
	 * @param containerId 容器ID
	 * @returns 格式化后的历史记录字符串
	 */
	formatChatHistory(containerId: string): string {
		const messages = this.chatThreadService.getCurrentThreadMessages(containerId);
		const assistantMessageLen = messages.filter(msg => msg.role === 'assistant').length;
		if (!messages || messages.length === 0 || assistantMessageLen === 0) {
			return '';
		}

		return messages.filter(msg => msg.role !== 'system').map((msg, index) => {
			// 基本格式：[角色]: 内容
			const roleLabel = this.getRoleLabel(msg.role);
			let content = '';

			if (msg.role === 'user') {
				// 用户消息优先使用 displayContent
				content = msg.displayContent ?? msg.content ?? '';
			} else if (msg.role === 'assistant') {
				// 助手消息优先使用 displayContent
				content = msg.displayContent ?? msg.content ?? '';
			} else if (msg.role === 'tool') {
				// 工具消息需要特殊处理
				content = this.formatToolMessage(msg);
			}

			// 添加消息序号
			const messageNumber = `#${index + 1}`;

			// 基本格式
			let formattedMessage = `${messageNumber} ${roleLabel}: ${content}`;

			// 用户消息，添加选择内容信息
			if (msg.role === 'user') {
				if (msg.state?.stagingSelections?.length > 0) {
					// const selectionsInfo = `\n    选择内容: ${msg.state.stagingSelections.length} 项`;
					// formattedMessage += selectionsInfo;
				}
			}

			return formattedMessage;
		}).join('\n\n');
	}

	/**
	 * 获取角色的显示标签
	 */
	private getRoleLabel(role: string): string {
		switch (role) {
			case 'user': return '用户';
			case 'assistant': return '助手';
			case 'system': return '系统';
			case 'tool': return '工具';
			default: return role;
		}
	}

	/**
	 * 格式化工具消息
	 */
	private formatToolMessage(toolMsg: ChatMessage): string {
		if (toolMsg.role === 'tool') {
			const name = toolMsg.toolCall.name || '工具';
			const result = toolMsg.content ?? '无结果';
			// 始终使用详细格式
			return `${name}\n    结果: ${result}`;
		}
		return toolMsg.content ?? '';
	}

	// 获取打开的标签页URI相对路径
	openTabs(): string {
		try {
			// 获取所有打开的编辑器，按顺序排列
			const editorIdentifiers = this.editorService.getEditors(EditorsOrder.SEQUENTIAL);

			const relativePaths = editorIdentifiers
				.filter(editor => editor.editor.resource !== undefined)
				.map(editor => editor.editor.resource?.fsPath)
				.filter(path => path !== undefined)
				.map(path => getRelativePath(this.workspaceService, path));
			return relativePaths.join('\n');
		} catch (error) {
			this.logger.error('获取打开的标签页失败:', error);
			return '';
		}
	}

}

registerSingleton(IChatAgentService, ChatAgentService, InstantiationType.Eager);
