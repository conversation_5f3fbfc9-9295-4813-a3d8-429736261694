import { ITodolistService, Step } from '../todolistService.js';
import { IToolsService } from '../../common/toolsService.js';
import { ChatMessage, userMessageOpts } from '../chatThreadType.js';
import { ToolCallStatus, ToolCallResultType, ToolNameEnum, InternalToolInfo, codeseekTools } from '../../common/toolsServiceTypes.js';
import { ICodeseekLogger } from '../../common/codeseekLogService.js';
import { ILLMMessageService } from '../../common/llmMessageService.js';
import { FeatureNames } from '../../common/codeseekSettingsTypes.js';
import { CodeAgentConvertMessageOpts, IConvertToLLMMessageService } from '../convertToLLMMessageService.js';
import { IInstantiationService } from '../../../../../platform/instantiation/common/instantiation.js';
import { IAgentManageService } from './agentManageService.js';
import { SendLLMType } from '../../common/llmMessageTypes.js';
import { BaseAgent, ExceededMaxLoopCountError, MAX_MESSAGES_SENT } from './baseAgent.js';
import { ChatMode } from '../../common/codeseekSettingsService.js';
import { IPluginTaskService } from '../pluginTaskService.js';
import { removeCloseThinkTag } from '../../common/helpers/common.js';

export type CodeAgentState = {
	[threadId: string]: ChatMessage[];
}

export type OsInformation = {
	operatingSystem: 'windows' | 'mac' | 'linux' | null;
	defaultShell: string;
}

export interface ICodeAgent {
	readonly _serviceBrand: undefined;

	act(containerId: string, threadId: string, stepIndex: number, userMessageOpts: userMessageOpts): Promise<void>;
}

export class CodeAgentService extends BaseAgent implements ICodeAgent {
	readonly _serviceBrand: undefined;
	private state: CodeAgentState = {};
	private isAborted = false;
	private currentContainerId?: string;
	private currentThreadId?: string;

	private commonTools: ToolNameEnum[] = [
		ToolNameEnum.READ_FILE,
		ToolNameEnum.EDIT_FILE,
		ToolNameEnum.LIST_DIR,
		ToolNameEnum.FILE_SEARCH,
		ToolNameEnum.GREP_SEARCH,
		ToolNameEnum.CODEBASE_SEARCH,
		ToolNameEnum.CALL_TRACE_QUERY,
	];

	private askInternalTools: ToolNameEnum[] = [
		...this.commonTools,
	];

	private agentInternalTools: ToolNameEnum[] = [
		...this.commonTools,

		ToolNameEnum.EXEC_COMMAND,
	];

	constructor(
		@ICodeseekLogger private readonly logger: ICodeseekLogger,
		@IToolsService private readonly toolsService: IToolsService,
		@ITodolistService private readonly todolistService: ITodolistService,
		@ILLMMessageService private readonly llmMessageService: ILLMMessageService,
		@IConvertToLLMMessageService private readonly convertToLLMMessageService: IConvertToLLMMessageService,
		@IInstantiationService override readonly instantiationService: IInstantiationService,
		@IPluginTaskService private readonly pluginTaskService: IPluginTaskService,
	) {
		super(instantiationService);
		this.instantiationService.invokeFunction(accessor => {
			const agentManageService = accessor.get(IAgentManageService);
			this._register(agentManageService.onDidAbort(cancelToken => {
				this.abort(cancelToken);
			}));
		});

	}

	private async preparePrompt(containerId: string, threadId: string, step: Step, userMessageOpts: userMessageOpts, validTools: InternalToolInfo[]) {

		const convertMessageOpts: CodeAgentConvertMessageOpts = {
			messagesType: 'codeAgentMessages',
			containerId,
			threadId,
			task: this.todolistService.state[threadId].task,
			taskContext: this.todolistService.state[threadId].taskContext,
			step,
			userMessageOpts,
			internalTools: validTools,
		};
		const prompt = await this.convertToLLMMessageService.prepareLLMChatMessages(convertMessageOpts);
		return prompt;
	}

	public async act(containerId: string, threadId: string, stepIndex: number, userMessageOpts: userMessageOpts) {
		// Reset abort state for new execution
		this.isAborted = false;

		// Store current execution context for abort
		this.currentContainerId = containerId;
		this.currentThreadId = threadId;

		this.todolistService.markStep(threadId, stepIndex, 'in_progress');
		const steps = this.todolistService.getAllStep(threadId);
		const isLastStep = userMessageOpts.chatMode === ChatMode.Agent && stepIndex === (steps?.length ?? 0) - 1;
		this.logger.info(`[codeAgentService] act: steps: ${JSON.stringify(steps)}`);
		if (!steps) return
		const internalToolCalls = userMessageOpts.chatMode === ChatMode.Agent ? this.agentInternalTools : this.askInternalTools
		const externalToolCalls: InternalToolInfo[] = []
		if (userMessageOpts.from === 'Plugin') {
			externalToolCalls.push(...(userMessageOpts.taskInfo?.externalTools ?? []));
		}
		const validTools = [...externalToolCalls];
		for (const tool of Object.values(codeseekTools)) {
			if (internalToolCalls.includes(tool.name as ToolNameEnum)) {
				validTools.push(tool);
			}
		}
		const prompt = await this.preparePrompt(containerId, threadId, steps[stepIndex], userMessageOpts, validTools);
		this.logger.info(`[codeAgentService] act: prompt: ${JSON.stringify(prompt)}`);
		let shouldSendAnotherMessage = true;
		let nAttempts = 0;
		this.state[threadId] = [
			...prompt.userPrompts.map(p => ({ role: 'user', content: p } as ChatMessage)),
		];
		const chatId = this.chatThreadService.getCurrentThreadChatId(threadId);
		while (shouldSendAnotherMessage && !this.isAborted) {
			if (this.isAborted) {
				this.logger.info(`[Agent] CodeAgentService act aborted for step ${stepIndex}`);
				this.todolistService.markStep(threadId, stepIndex, 'failed', { content: '', summary: 'Task was aborted by user' });
				// Clear stream state when aborted
				this.chatThreadService.chatEnd(containerId, threadId);
				return;
			}

			shouldSendAnotherMessage = false;
			if (nAttempts > MAX_MESSAGES_SENT) {
				this.logger.info(`[Agent] CodeAgentService tool exceeded max call count`);
				this.todolistService.markStep(threadId, stepIndex, 'blocked');
				throw new ExceededMaxLoopCountError('Tool exceeded max call count')
			}
			nAttempts += 1;
			let res_: () => void;
			const awaitable = new Promise<void>((res, rej) => { res_ = res; });

			let messages = this.state[threadId].map(m => (this.toLLMChatMessage(m)));
			const chatMessages: SendLLMType = {
				messagesType: 'chatMessages',
				messages: [
					{ role: 'system', content: prompt.systemPrompt },
					...messages,
				],
			}
			let toolCallResults: ToolCallResultType[] = [];
			const cancelToken = this.llmMessageService.sendLLMMessage({
				containerId,
				...chatMessages,
				userId: userMessageOpts.workspaceInfo?.userId ?? '',
				useProviderFor: FeatureNames.CtrlL,
				logging: { loggingName: userMessageOpts.chatMode },
				onText: ({ fullText }) => { },
				onFinalMessage: async ({ fullText, toolCalls }) => {
					fullText = removeCloseThinkTag(fullText);
					this.logger.info(`[Agent] CodeAgentService fullText: ${fullText}, toolCalls: ${JSON.stringify(toolCalls)}`);
					if (userMessageOpts.from === 'Plugin') {
						this.pluginTaskService.fireReceiveMessage(userMessageOpts.taskInfo.taskId, { message: { fullText: fullText } });
					}

					const extractToolResult = this.extractTool(fullText, validTools);
					this.logger.info(`[Agent] CodeAgentService extractToolResult: ${JSON.stringify(extractToolResult)}`);
					if (extractToolResult.status === 'error') {
						this.state[threadId].push({ role: 'assistant', content: fullText, displayContent: fullText, chatId });
						this.logger.warn('[Agent] ChatAgentService extract tool format error');
						this.state[threadId].push({ role: 'user', content: extractToolResult.error, displayContent: extractToolResult.error, state: {} } as ChatMessage);
						shouldSendAnotherMessage = true;
					} else {
						toolCalls?.push(...extractToolResult.toolCalls);
						let fullText_: string[] = [];
						let isCallEdit: boolean | undefined = false;
						try {
							const finalAnswer = this.extractFinalAnswer(fullText);
							const thinking = this.extractThinking(fullText);
							if (thinking) {
								fullText_.push(thinking);
							}
							if (finalAnswer) {
								fullText_.push(finalAnswer);
							}
							if (fullText_.length > 0 && !isLastStep) {
								await this.handleText('', fullText_.join('\n\n'), containerId, threadId, false, () => this.isAborted);
							}
							this.state[threadId].push({ role: 'assistant', content: fullText, displayContent: fullText, chatId });
							this.chatThreadService.setStreamState(containerId, threadId, { messageSoFar: undefined });
							if ((toolCalls?.length ?? 0) === 0) {
								if (fullText.includes(this.AGENT_END_TAG)) {
									this.logger.info(`[Agent] CodeAgentService end of agent`);
									const summary = this.extractSummary(fullText);
									if (!isLastStep) {
										this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText_.join('\n\n'), displayContent: fullText_.join('\n\n'), chatId });
									} else {
										this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText_.join('\n\n'), displayContent: fullText_.join('\n\n'), chatId }, false);
									}
									this.todolistService.markStep(threadId, stepIndex, 'completed', { content: finalAnswer, summary });
								} else {
									this.logger.info(`[Agent] CodeAgentService format error`);
									this.state[threadId].push({ role: 'user', content: this.FORMAT_ERROR_MESSAGE, displayContent: this.FORMAT_ERROR_MESSAGE, state: {} } as ChatMessage);
									shouldSendAnotherMessage = true;
								}
							}
							else {
								this.logger.info(`[Agent] CodeAgentService tool call`);
								isCallEdit = toolCalls?.some(toolCall => toolCall.name === ToolNameEnum.EDIT_FILE);
								if (!isCallEdit) {
									this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText_.join('\n\n'), displayContent: fullText_.join('\n\n'), chatId });
								}
								const isCallFormatOK = toolCalls?.every(toolCall => validTools.some(t => t.name === toolCall.name));
								if (!isCallFormatOK) {
									this.state[threadId].push({ role: 'user', content: this.TOOL_FORMAT_ERROR_MESSAGE, displayContent: this.TOOL_FORMAT_ERROR_MESSAGE, state: {} } as ChatMessage);
									shouldSendAnotherMessage = true;
								} else {
									for (const [index, toolCall] of (toolCalls ?? []).entries()) {
										// Check for abort status before each tool call
										if (this.isAborted) {
											this.logger.info(`[Agent] CodeAgentService tool call aborted for step ${stepIndex}`);
											this.todolistService.markStep(threadId, stepIndex, 'failed', { content: '', summary: 'Task was aborted by user' });
											return;
										}

										this.chatThreadService.setStreamState(containerId, threadId, {
											tool: {
												toolCall,
												toolCallResult: {
													status: ToolCallStatus.idle,
													name: toolCall.name as ToolNameEnum,
													content: '',
													error: undefined,
													toolCallReturn: null,
												},
											}
										});
										if (toolCall.name === ToolNameEnum.EDIT_FILE) {
											const code = '\n\n' + this.extractEditFileTool([toolCall])
											await this.handleText(fullText_.join('\n\n'), code, containerId, threadId, true, () => this.isAborted);
											fullText_.push(code);
											isCallEdit = toolCalls?.slice(index + 1).some(toolCall => toolCall.name === ToolNameEnum.EDIT_FILE);
											if (!isCallEdit) {
												this.chatThreadService.addMessageToThread(containerId, threadId, { role: 'assistant', content: fullText_.join('\n\n'), displayContent: fullText_.join('\n\n'), chatId });
											}
										}
										if (userMessageOpts.from === 'Plugin') {
											this.pluginTaskService.fireReceiveMessage(userMessageOpts.taskInfo.taskId, { message: { toolCall: toolCall } });
										}
										const toolCallResult: ToolCallResultType = await this.toolsService.executeTool(containerId, threadId, chatId, toolCall, userMessageOpts);
										this.logger.info(`[Agent] CodeAgentService toolCallResult: ${JSON.stringify(toolCallResult)}`);
										if (userMessageOpts.from === 'Plugin') {
											this.pluginTaskService.fireReceiveMessage(userMessageOpts.taskInfo.taskId, { message: { toolCall: toolCall, toolCallResult: toolCallResult } });
										}
										toolCallResults.push(toolCallResult);
										this.chatThreadService.updateToolCall(containerId, threadId, {
											toolCallResult
										});
										this.chatThreadService.addMessageToThread(containerId, threadId, {
											role: 'tool',
											content: toolCallResult.content,
											toolCall: this.chatThreadService.streamState[containerId]?.[threadId]?.tool?.toolCall || toolCall,
											toolCallResult: toolCallResult,
											chatId
										});
										shouldSendAnotherMessage = true;
										this.chatThreadService.setStreamState(containerId, threadId, { tool: undefined });
										await new Promise(resolve => setTimeout(resolve, 1000));
									}
									const content = this.joinToolResult(toolCallResults);
									this.state[threadId].push({ role: 'tool', content: content } as ChatMessage);
								}
							}
						} catch (error) {
							this.logger.error(`[Agent] CodeAgentService error: ${error}`);
							if (userMessageOpts.from === 'Plugin') {
								this.pluginTaskService.fireTaskError(userMessageOpts.taskInfo.taskId);
							}
							this.chatThreadService.finishStreamingTextMessage(containerId, threadId, this.chatThreadService.streamState[containerId]?.[threadId]?.messageSoFar ?? '', error);
							throw error;
						}
					}
					res_();
				},
				onError: (error) => {
					this.todolistService.markStep(threadId, stepIndex, 'failed', { content: '', summary: '' });
					this.chatThreadService.finishStreamingTextMessage(containerId, threadId, this.chatThreadService.streamState[containerId]?.[threadId]?.messageSoFar ?? '', error);
					res_();
				},
			});
			if (cancelToken === null) break;
			this.chatThreadService.setStreamState(containerId, threadId, { streamingToken: cancelToken, isLoading: true });

			await awaitable;
		}
	}

	public abort(cancelToken: string) {
		this.logger.info(`[Agent] CodeAgentService aborting requestId: ${cancelToken}`);
		this.isAborted = true;

		// Immediately clear stream state if we have execution context
		if (this.currentContainerId && this.currentThreadId) {
			this.chatThreadService.chatEnd(this.currentContainerId, this.currentThreadId);
		}

		this.llmMessageService.abort(cancelToken);
	}
}
