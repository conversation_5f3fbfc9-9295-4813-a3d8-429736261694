/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { ICodeEditorService } from '../../../../editor/browser/services/codeEditorService.js';
import { IEditCodeService } from './editCodeService.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { ICodeEditor } from '../../../../editor/browser/editorBrowser.js';
import { IWorkbenchContribution, registerWorkbenchContribution2, WorkbenchPhase } from '../../../common/contributions.js';
import { IQuickEditStateService } from './quickEditStateService.js';
import { roundRangeToLines } from './helpers/util.js';
import { InlineCompletionsController } from '../../../../editor/contrib/inlineCompletions/browser/controller/inlineCompletionsController.js';
import { Action2, registerAction2 } from '../../../../platform/actions/common/actions.js';
import { ServicesAccessor } from '../../../../platform/instantiation/common/instantiation.js';
import { KeybindingWeight } from '../../../../platform/keybinding/common/keybindingsRegistry.js';
import { KeyCode, KeyMod } from '../../../../base/common/keyCodes.js';
import { localize2 } from '../../../../nls.js';
import { CODESEEK_EXPLAIN_CODE_ACTION_ID, CODESEEK_REFACTOR_CODE_ACTION_ID } from './actionIDs.js';
// import { IMetricsService } from '../common/metricsService.js';
import { ICodeseekCommandService } from './codeseekCommadService.js';
import { EDITOR_LEFT_MARGIN_WIDTH } from './media/index.js';
import { EditorOption } from '../../../../editor/common/config/editorOptions.js';

/**
 * Manages tip-related event listeners for code editors, tracking cursor and focus changes.
 * Automatically registers listeners for existing and newly added editors to handle tip zone interactions.
 */
class ContentSelectionListener extends Disposable {
	constructor(
		onDidTip: () => void,
		@ICodeEditorService private readonly _editorService: ICodeEditorService,
		@IQuickEditStateService private readonly quickEditStateService: IQuickEditStateService,
		@IEditCodeService private readonly editCodeService: IEditCodeService,
	) {
		super();

		const addTipListeners = (editor: ICodeEditor) => {
			// 监听光标位置变化事件
			this._register(editor.onDidChangeCursorPosition(() => {
				onDidTip();
			}));

			// 监听文本选择事件
			this._register(editor.onDidChangeCursorSelection(() => {
				onDidTip();
			}));

			// 监听内容变化事件
			this._register(editor.onDidChangeModelContent(() => {
				onDidTip();
			}));

			// 监听编辑器不聚焦事件
			this._register(editor.onDidBlurEditorText(() => {
				this.editCodeService.disposeTip();
			}));

			this._register(editor.onDidFocusEditorText(() => {
				onDidTip();
			}));

			this._register(this.quickEditStateService.onDidFocusChat(() => {
				this.editCodeService.disposeTip();
			}));
		};

		const initializeEditor = (editor: ICodeEditor) => {
			addTipListeners(editor);
		};

		// initialize current editors + any new editors
		for (const editor of this._editorService.listCodeEditors()) {
			initializeEditor(editor);
		}
		this._register(this._editorService.onCodeEditorAdd(editor => {
			initializeEditor(editor);
		}));
	}
}


/**
 * Contributes tip functionality to the workbench, managing tip zones for text selections and blank lines in code editors.
 * Handles creating and disposing of tip zones based on cursor position, selection, and editor focus events.
 */
class TipContribution extends Disposable implements IWorkbenchContribution {
	static readonly ID = 'workbench.contrib.codeseek.tip';
	private ghostTextCheckInterval?: any;

	constructor(
		@IInstantiationService private readonly instantiationService: IInstantiationService,
		@IEditCodeService private readonly editCodeService: IEditCodeService,
		@ICodeEditorService private readonly codeEditorService: ICodeEditorService,
	) {
		super();

		// 启动定时检查器
		this.startGhostTextChecker();

		const onDidTip = () => {
			this.editCodeService.disposeTip();
			const editor = this.codeEditorService.getActiveCodeEditor();
			if (!editor) return;

			const selection = editor.getSelection();
			if (!selection) return;

			const model = editor.getModel();
			if (!model) return;

			const lines = roundRangeToLines(selection, { emptySelectionBehavior: 'null' });
			if (lines) {
				const { startLineNumber: startLine, endLineNumber: endLine } = lines;

				if (startLine <= 0 || endLine > model.getLineCount() ||
					startLine > endLine ||
					(startLine === endLine &&
						selection.startColumn === selection.endColumn)) {
					return;
				}

				this.editCodeService.addTipZone({
					editor,
					tipArea: {
						type: 'TextSelectionTipZone',
						startLine,
						endLine
					}
				});
			} else {
				const lineNumber = selection.positionLineNumber;
				if (lineNumber <= 0 || lineNumber > model.getLineCount()) {
					return;
				}

				const lineContent = model.getLineContent(lineNumber);
				if (lineContent?.trim() === '' && !this.hasGhostText(editor, lineNumber)) {
					const topPx = editor.getTopForLineNumber(lineNumber) - editor.getScrollTop();
					const indentation = lineContent.match(/^\s*/)?.[0] || '';
					const indentWidth = editor.getOption(EditorOption.fontInfo).spaceWidth * indentation.length;
					const initialLeftPx = editor.getScrolledVisiblePosition({ lineNumber: 1, column: 1 })?.left ?? EDITOR_LEFT_MARGIN_WIDTH;
					const leftPx = initialLeftPx + indentWidth + 20;
					this.editCodeService.addTipZone({
						editor,
						tipArea: { type: 'BlankLineTipZone', lineNumber, column: 0, showPosition: { leftPx, topPx } }
					});
				}
			}
		};

		this._register(this.instantiationService.createInstance(
			ContentSelectionListener,
			() => { onDidTip(); },
		));
	}

	private startGhostTextChecker() {
		this.ghostTextCheckInterval = setInterval(() => {
			const editor = this.codeEditorService.getActiveCodeEditor();
			if (!editor) return;

			const selection = editor.getSelection();
			if (!selection) return;

			const lineNumber = selection.positionLineNumber;
			if (this.hasGhostText(editor, lineNumber)) {
				this.editCodeService.disposeTip('BlankLineTipZone');
			}
		}, 500);

		this._register({
			dispose: () => {
				if (this.ghostTextCheckInterval) {
					clearInterval(this.ghostTextCheckInterval);
				}
			}
		});
	}

	private hasGhostText(editor: ICodeEditor, lineNumber: number): boolean {
		const controller = InlineCompletionsController.get(editor);
		if (!controller) return false;

		const model = controller.model.get();
		if (!model) return false;

		const state = model.state.get();
		if (!state || state.kind !== 'ghostText') return false;

		const ghostText = state.primaryGhostText;
		return ghostText && !ghostText.isEmpty() && ghostText.lineNumber === lineNumber;
	}
}

registerWorkbenchContribution2(TipContribution.ID, TipContribution, WorkbenchPhase.BlockRestore);


registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_EXPLAIN_CODE_ACTION_ID,
			title: localize2('codeseekExplainCode', 'Flow: explain code'),
			f1: true,
			keybinding: {
				primary: KeyMod.CtrlCmd | KeyMod.Alt | KeyCode.KeyE,
				weight: KeybindingWeight.WorkbenchContrib
			}
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(ICodeEditorService);
		// const metricsService = accessor.get(IMetricsService);
		const codeseekCommandService = accessor.get(ICodeseekCommandService);
		// metricsService.capture('Ctrl+Alt+E', {});

		const editor = editorService.getActiveCodeEditor();
		if (!editor) return;
		const model = editor.getModel();
		if (!model) return;
		const selection = roundRangeToLines(editor.getSelection(), { emptySelectionBehavior: 'line' });
		if (!selection) return;
		const { startLineNumber: startLine, endLineNumber: endLine } = selection;
		codeseekCommandService.initializeExplainStream({ startLine, endLine, editor });
	}
});


registerAction2(class extends Action2 {
	constructor() {
		super({
			id: CODESEEK_REFACTOR_CODE_ACTION_ID,
			title: localize2('codeseekRefactorCode', 'Flow: refactor code'),
			f1: true,
			keybinding: {
				primary: KeyMod.CtrlCmd | KeyMod.Alt | KeyCode.KeyR,
				weight: KeybindingWeight.WorkbenchContrib
			}
		});
	}

	async run(accessor: ServicesAccessor): Promise<void> {
		const editorService = accessor.get(ICodeEditorService);
		const codeseekCommandService = accessor.get(ICodeseekCommandService);

		const editor = editorService.getActiveCodeEditor();
		if (!editor) return;
		const model = editor.getModel();
		if (!model) return;
		const selection = roundRangeToLines(editor.getSelection(), { emptySelectionBehavior: 'line' });
		if (!selection) return;
		const { startLineNumber: startLine, endLineNumber: endLine } = selection;
		codeseekCommandService.initializeRefactorStram({ startLine, endLine, editor });
	}
});
