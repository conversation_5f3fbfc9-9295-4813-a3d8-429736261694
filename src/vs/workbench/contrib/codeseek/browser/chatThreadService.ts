/*--------------------------------------------------------------------------------------
 *  Copyright 2025 ZTE, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../base/common/lifecycle.js';
import { InstantiationType, registerSingleton } from '../../../../platform/instantiation/common/extensions.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { IEditorService } from '../../../services/editor/common/editorService.js';
import { CodebaseSelection, StagingSelectionItem } from '../common/selectedFileServiceType.js';
import { AskResponse, ToolCallResultType, ToolCallType } from '../common/toolsServiceTypes.js';
import { ICodeseekCodeSelectionService } from './codeseekCodeSelectionService.js';
import { ISidebarStateService } from './sidebarStateService.js';
import { THREAD_MESSAGES_STORAGE_KEY, THREAD_ABSTRACT_STORAGE_KEY } from '../common/storageKeys.js';
import { URI } from '../../../../base/common/uri.js';
import { ChatContainers, ChatMessage, ChatThreads, ContainerState, ThreadStreamState, ThreadType, ThreadsState, UserMessageState, userMessageOpts, WorkspaceInfo, StateSelections, NoStorageState, ToolCallState } from './chatThreadType.js';
import { IViewsService } from '../../../services/views/common/viewsService.js';
import { SyncDescriptor } from '../../../../platform/instantiation/common/descriptors.js';
import { IViewContainersRegistry, IViewsRegistry, ViewContainerLocation, Extensions as ViewExtensions } from '../../../common/views.js';
import { ViewPaneContainer } from '../../../browser/parts/views/viewPaneContainer.js';
import { Orientation } from '../../../../base/browser/ui/sash/sash.js';
import { localize2 } from '../../../../nls.js';
import { Registry } from '../../../../platform/registry/common/platform.js';
import { SidebarViewPane } from './sidebarPane.js';
import { CODESEEK_NEW_CHAT_ACTION_ID, CODESEEK_VIEW_CONTAINER_ID, CODESEEK_VIEW_CONTAINER_ID_KEY } from './actionIDs.js';
import { ICommandService } from '../../../../platform/commands/common/commands.js';
import { Codicon } from '../../../../base/common/codicons.js';
import { IInstantiationService } from '../../../../platform/instantiation/common/instantiation.js';
import { IAgentManageService } from './agent/agentManageService.js';
import { ChatMode, ICodeseekSettingsService } from '../common/codeseekSettingsService.js';
import { ICodeseekLogger } from '../common/codeseekLogService.js';
import { chat_systemMessage, chat_userMessageContent, fix_systemMessage, user_rules } from '../common/prompt/prompts.js';
import { ICodeseekFileService } from '../common/codeseekFileService.js';
import { ILLMMessageService } from '../common/llmMessageService.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { ICodeSeekExporerService } from '../common/codeseekExporerService.js';
import { IModelService } from '../../../../editor/common/services/model.js';
import { IMetricsService, METRICS_EVENT } from '../common/metricsService.js';
import { IUrlContentFetcherService } from '../common/IUrlContentFetchService.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { ICodeseekUacLoginService } from '../common/uac/UacloginTypes.js';
import { LLMChatMessage, SendLLMType, toLLMChatMessage } from '../common/llmMessageTypes.js';
import { FeatureNames } from '../common/codeseekSettingsTypes.js';
import { findLastIndex, removeCloseThinkTag, removeThinkTag } from '../common/helpers/common.js';
import { osType } from './helpers/systemInfo.js';
import { getRelativePath, getWorkspaceUri } from '../common/helpers/path.js';
import { EditorsOrder } from '../../../common/editor.js';
import { IContextKeyService } from '../../../../platform/contextkey/common/contextkey.js';
import { IDirectoryStrService } from '../common/directoryStrService.js';
import { isLinux, isMacintosh, isWindows } from '../../../../base/common/platform.js';
import { IConfigurationService } from '../../../../platform/configuration/common/configuration.js';
import { TerminalProfile } from './pluginTaskService.js';
import { SHELL_PATHS } from '../electron-main/utils/index.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { CancellationTokenSource } from '../../../../base/common/cancellation.js';
import { ICodebaseSearchService } from './codebaseSearchService.js';
import { IEditCodeService } from './editCodeService.js';
import { IMentionService } from '../common/selectedFileService.js';

let isContainerLoaded = false;
const MAX_CONTAINERS = 3;
let order = 2;

// 添加Apply操作状态管理
interface ApplyingFileState {
	isApplying: boolean;
	applyStartTime: number;
	originalSelections?: StagingSelectionItem[];
}

const defaultMessageState: UserMessageState = {
	stagingSelections: [],
	isBeingEdited: false,
};

const newThreadObject = () => {
	const now = new Date().toISOString();
	return {
		id: generateUuid(),
		createdAt: now,
		lastModified: now,
		messagesLength: 0,
		firstUserMessage: '',
		state: {
			stateSelections: { list: [], followEditorActive: true },
			focusedMessageIdx: undefined,
			isCheckedOfSelectionId: {}
		},

	} satisfies ChatThreads[string];
};

export const IChatThreadService = createDecorator<IChatThreadService>('codeseekChatThreadService');
export interface IChatThreadService {
	readonly _serviceBrand: undefined;

	readonly containerState: ContainerState;
	readonly streamState: ThreadStreamState;

	setStreamState(containerId: string, threadId: string, state: Partial<NonNullable<ThreadStreamState[string][string]>>): void;

	onDidChangeCurrentContainer: Event<void>;
	onDidChangeCurrentThread: Event<string>;
	onDidChangeStreamState: Event<{ containerId: string, threadId: string }>;
	onDidSetChatTitle: Event<{ containerId: string, message: string }>;

	openNewContainer(): string;
	openNewThread(containerId: string): void;

	getCurrentContainerId(): string;
	getCurrentThreadId(containerId: string): string;

	isCurrentThreadWorking(containerId: string): boolean;
	getCurrentThread(containerId: string): ChatThreads[string];
	getCurrentThreadMessages(containerId: string): ChatMessage[];

	switchToContainer(containerId: string): void;
	deleteContainer(containerId: string): void;
	switchToThread(containerId: string, threadId: string, targetContainerId?: string): void;
	deleteThread(containerId: string, threadId: string): void;

	isFocusingContainer(containerId: string): boolean;

	// you can edit multiple messages
	// the one you're currently editing is "focused", and we add items to that one when you press cmd+L.
	getFocusedMessageIdx(containerId: string): number | undefined;
	isFocusingMessage(containerId: string): boolean;
	setFocusedMessageIdx(containerId: string, messageIdx: number | undefined): void;

	// exposed getters/setters
	getCurrentMessageState(containerId: string, messageIdx: number): UserMessageState;
	setCurrentMessageState(containerId: string, messageIdx: number, newState: Partial<UserMessageState>): void;
	getCurrentThreadStagingSelections(containerId: string): StagingSelectionItem[];
	setCurrentThreadStagingSelections(containerId: string, stagingSelections: StagingSelectionItem[]): void;
	getCurrentThreadStateSelections(containerId: string): StateSelections;
	setCurrentThreadStateSelectionsChangeSelections(containerId: string): void;

	// call to edit a message
	editUserMessageAndStreamResponse({ containerId, threadId, userMessage, chatMode, messageIdx }: { containerId: string; threadId: string; userMessage: string; chatMode: ChatMode; messageIdx: number }): Promise<void>;

	// call to add a message
	addUserMessageAndStreamResponse({ containerId, userMessageOpts, isFromAgent }: { containerId: string; userMessageOpts: userMessageOpts; isFromAgent?: boolean }): Promise<void>;
	addUserMessageAndStreamResponseWithAgent({ containerId, userMessageOpts }: { containerId: string; userMessageOpts: userMessageOpts }): Promise<void>;

	cancelStreaming(containerId: string, threadId: string): void;
	dismissStreamError(containerId: string, threadId: string): void;
	setAskResponse(threadId: string, askResponse: AskResponse): void;
	addSelectionToChat(containerId: string, selection?: StagingSelectionItem): void;
	cancelExecTool(threadId: string): void;
	addMessageToThread(containerId: string, threadId: string, message: ChatMessage, affectCurrentThread?: boolean): void;
	finishStreamingTextMessage(containerId: string, threadId: string, content: string, error?: { message: string; fullError: Error | null; isExceededMaxLoopCount?: boolean }, affectCurrentThread?: boolean): void;
	chatEnd(containerId: string, threadId: string, error?: { message: string; fullError: Error | null; isExceededMaxLoopCount?: boolean }): void;

	switchToAgent(containerId: string, threadId: string): void;
	switchToAsk(containerId: string, threadId: string): void;
	getCurrentThreadChatId(threadId: string): string;
	getCurrentThreadBusinessEvent(threadId: string): string;

	getNoStorageState(threadId: string): NoStorageState[string] | undefined;
	setNoStorageState(threadId: string, state: Partial<NoStorageState[string]>): void;

	// Apply状态管理方法
	setApplyingState(fileUri: string, isApplying: boolean, containerId?: string): void;
	getApplyingState(fileUri: string): ApplyingFileState | undefined;

	updateToolCall(containerId: string, threadId: string, state: Partial<ToolCallState>): void;
}

export class ChatThreadService extends Disposable implements IChatThreadService {
	_serviceBrand: undefined;

	// this fires when the current thread changes at all (a switch of currentThread, or a message added to it, etc)
	private readonly _onDidChangeCurrentThread = new Emitter<string>();
	readonly onDidChangeCurrentThread: Event<string> = this._onDidChangeCurrentThread.event;

	private readonly _onDidChangeCurrentContainer = new Emitter<void>();
	readonly onDidChangeCurrentContainer: Event<void> = this._onDidChangeCurrentContainer.event;

	private readonly _onDidSetChatTitle = new Emitter<{ containerId: string, message: string }>();
	readonly onDidSetChatTitle: Event<{ containerId: string, message: string }> = this._onDidSetChatTitle.event;

	containerState: ContainerState;
	readonly streamState: ThreadStreamState = {};
	readonly workspaceDirectoryTree: Record<string, string> = {};

	private readonly _onDidChangeStreamState = new Emitter<{ containerId: string, threadId: string }>();
	readonly onDidChangeStreamState: Event<{ containerId: string, threadId: string }> = this._onDidChangeStreamState.event;
	private readonly threadId2CancelTokens: Map<string, CancellationTokenSource> = new Map();
	private readonly threadId2ChatIds: Map<string, string> = new Map();
	private readonly noStorageState: NoStorageState = {};

	// 添加Apply状态管理 - 单例服务需要管理全局Apply状态
	private readonly applyingFileState: Map<string, ApplyingFileState> = new Map();

	constructor(
		@IStorageService private readonly _storageService: IStorageService,
		@ICodeseekFileService private readonly _codeseekFileService: ICodeseekFileService,
		@ILLMMessageService private readonly _llmMessageService: ILLMMessageService,
		@IWorkspaceContextService private readonly _workspaceContextService: IWorkspaceContextService,
		@ICodeSeekExporerService private readonly _codeSeekExporerService: ICodeSeekExporerService,
		@IModelService private readonly _modelService: IModelService,
		@ISidebarStateService private readonly _sidebarStateService: ISidebarStateService,
		@ICodeseekCodeSelectionService private readonly _codeSeekCodeSelectionService: ICodeseekCodeSelectionService,
		@IEditorService private readonly _editorService: IEditorService,
		@ICodeseekSettingsService private readonly _codeseekSettingsService: ICodeseekSettingsService,
		@ICodeseekLogger private readonly _codeseekLogService: ICodeseekLogger,
		@IMetricsService private readonly _metricsService: IMetricsService,
		@IMentionService private readonly _mentionsService: IMentionService,
		@IUrlContentFetcherService private readonly _urlContentFetcher: IUrlContentFetcherService,
		@INotificationService private readonly _notificationService: INotificationService,
		@ICodeseekUacLoginService private readonly _zteUserInfoService: ICodeseekUacLoginService,
		@IViewsService private readonly _viewsService: IViewsService,
		@ICommandService private readonly _commandService: ICommandService,
		@IInstantiationService private readonly _instantiationService: IInstantiationService,
		@IContextKeyService private readonly _contextKeyService: IContextKeyService,
		@IDirectoryStrService private readonly _directoryStrService: IDirectoryStrService,
		@IConfigurationService private readonly _configurationService: IConfigurationService,
		@ICodebaseSearchService private readonly _codebaseSearchService: ICodebaseSearchService,
		@IEditCodeService private readonly _editCodeService: IEditCodeService,
	) {
		super();

		this.containerState = {
			allContainers: {
				[CODESEEK_VIEW_CONTAINER_ID]: {
					threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
				}
			},
			currentContainerId: CODESEEK_VIEW_CONTAINER_ID
		};

		const allContainers = this._readAllContainer();
		if (allContainers) {
			this.containerState.allContainers = allContainers;
			this._registerExistingContainers();
		}

		if (!this.getCurrentThreadId(this.containerState.currentContainerId) || this.getCurrentThreadId(this.containerState.currentContainerId) === '') {
			this.openNewThread(this.containerState.currentContainerId);
		}

		this._editorService.onDidActiveEditorChange(() => this.onDidActiveEditorChange(this.containerState.currentContainerId));
		this._codeSeekCodeSelectionService.onDidAddContentFromEditor((selection) => {
			if (selection.type === 'File' && this.isFileApplying(selection.fileURI.toString())) {
				return; // 跳过Apply操作期间的文件选择
			}
			this.addSelectionToChat(this.containerState.currentContainerId, selection);
		});
		this._codeSeekCodeSelectionService.onDidAddCodeBlock((selection) => {

			if (selection.type === 'File' && this.isFileApplying(selection.fileURI.toString())) {
				return; // 跳过Apply操作期间的文件选择
			}
			this.addSelectionToChat(this.containerState.currentContainerId, selection);
		});

		// 监听Apply操作事件来管理状态
		this._register(this._editCodeService.onDidStartApplying(({ uri, applyId }: { uri: any; applyId: string }) => {
			this.setApplyingState(uri.toString(), true);
		}));
		this._register(this._editCodeService.onDidEndApplying(({ uri, applyId }: { uri: any; applyId: string }) => {
			this.setApplyingState(uri.toString(), false);
		}));

		this._viewsService.onDidChangeViewContainerVisibility(event => {
			if (event.visible && event.id.startsWith(CODESEEK_VIEW_CONTAINER_ID_KEY)) {
				this.containerState.currentContainerId = event.id;
				this._onDidChangeCurrentContainer.fire();
				this._onDidChangeCurrentThread.fire(event.id);
				if (isContainerLoaded) {
					isContainerLoaded = false;
					Object.keys(this.containerState.allContainers).forEach(containerId => {
						const container = this.containerState.allContainers[containerId];
						const messages = container.threadsState.currentThreadMessages;
						if (messages && messages.length > 0) {
							const firstUserMessage = messages.find((m: ChatMessage) => m.role === 'user');
							if (firstUserMessage && firstUserMessage.displayContent) {
								this._onDidSetChatTitle.fire({ containerId, message: firstUserMessage.displayContent });
							}
						}
					});
				}
			}
		});

		this._codeseekSettingsService.onDidChangeChatMode(({ containerId, chatMode }) => {
			if (chatMode === ChatMode.Agent) {
				const currentThreadStagingSelections = this.getCurrentThreadStagingSelections(containerId);
				let codeSelections: StagingSelectionItem[] = [];
				for (const selection of currentThreadStagingSelections) {
					if (selection.type === 'Codebase') {
						continue;
					}
					codeSelections.push(selection);
				}
				this.setCurrentThreadStagingSelections(containerId, codeSelections);
			}
		});
	}

	public openNewContainer(): string {
		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewsRegistry = Registry.as<IViewsRegistry>(ViewExtensions.ViewsRegistry);

		const allContainerIds = Object.keys(this.containerState.allContainers);

		if (allContainerIds.length >= MAX_CONTAINERS) {
			this._commandService.executeCommand(CODESEEK_NEW_CHAT_ACTION_ID);
			return this.containerState.currentContainerId;
		}

		const usedOrders = new Set<number>();
		for (const id of allContainerIds) {
			const orderStr = id.split('.')[3];
			const orderNum = parseInt(orderStr);
			if (!isNaN(orderNum)) {
				usedOrders.add(orderNum);
			}
		}

		let nextOrder = 2;
		while (usedOrders.has(nextOrder)) {
			nextOrder++;
		}

		const newContainerId = `workbench.codeseek.container.${nextOrder}.view`;
		const newViewId = newContainerId;

		const newContainer = viewContainersRegistry.registerViewContainer({
			id: newContainerId,
			title: localize2('newCodeseekContainer', 'New Chat'),
			ctorDescriptor: new SyncDescriptor(
				ViewPaneContainer, [newContainerId, {
					mergeViewWithContainerWhenSingleView: true,
					orientation: Orientation.HORIZONTAL,
				}]
			),
			hideIfEmpty: false,
			order: nextOrder,
			rejectAddedViews: true,
			icon: Codicon.symbolMethod,
		}, ViewContainerLocation.AuxiliaryBar, { doNotRegisterOpenCommand: true, isDefault: true });

		viewsRegistry.registerViews([{
			id: newViewId,
			hideByDefault: false,
			name: localize2('newCodeseekChat', 'New Chat'),
			ctorDescriptor: new SyncDescriptor(SidebarViewPane),
			canToggleVisibility: false,
			canMoveView: false,
			weight: 80,
			order: 1,
		}], newContainer);

		this.containerState.currentContainerId = newContainerId;
		this.containerState.allContainers[newContainerId] = {
			threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
		};
		this.openNewThread(newContainerId);

		order = Math.max(order, nextOrder + 1);

		return newContainerId;
	}

	openNewThread(containerId: string) {
		const currentThread = this.getCurrentThread(containerId);
		if (currentThread && this.isCurrentThreadWorking(containerId)) {
			const threadId = currentThread.id
			this.cancelStreaming(containerId, threadId);
		}

		this._sidebarStateService.fireOpenNewChat(containerId);
		// if a thread with 0 messages already exists, switch to it
		const { allThreads } = this.containerState.allContainers[containerId].threadsState;
		for (const threadId in allThreads) {
			if (allThreads[threadId].messagesLength === 0) {
				allThreads[threadId].state.focusedMessageIdx = undefined;
				allThreads[threadId].state.stateSelections = { list: [], followEditorActive: true };
				allThreads[threadId].state.isCheckedOfSelectionId = {};
				this.switchToThread(containerId, threadId);
				this._addSelectionToChat(containerId);
				return;
			}
		}
		// otherwise, start a new thread
		const newThread = newThreadObject();

		// update state
		const newThreads: ChatThreads = {
			...allThreads,
			[newThread.id]: newThread
		};
		this._onDidSetChatTitle.fire({ containerId, message: 'New Chat' });
		this._setState(containerId, { allThreads: newThreads, currentThreadId: newThread.id, currentThreadMessages: [] }, true);
		this._codeseekSettingsService.setChatMode(this._codeseekSettingsService.getChatModeForContainer(containerId), { containerId });
		this._storeChatSummary();
		this._addSelectionToChat(containerId);
	}

	private _convertThreadDataFromStorage(threadsStr: string): ChatContainers | ChatThreads | ChatMessage[] {
		const data = JSON.parse(threadsStr, (key, value) => {
			if (value && typeof value === 'object' && value.$mid === 1) {
				return URI.from(value);
			}
			return value;
		});

		if (data && typeof data === 'object') {
			if ('allThreads' in data && 'currentThreadId' in data && !('allContainers' in data)) {
				const oldFormat = data as unknown as ThreadsState;
				const newFormat: ChatContainers = {
					[CODESEEK_VIEW_CONTAINER_ID]: {
						threadsState: {
							allThreads: oldFormat.allThreads || {},
							currentThreadId: oldFormat.currentThreadId || '',
							currentThreadMessages: []
						}
					}
				};
				return newFormat;
			}
		}

		return data;
	}

	private _readAllContainer(): ChatContainers | null {
		try {
			const containerMapStr = this._storageService.get(THREAD_ABSTRACT_STORAGE_KEY, StorageScope.WORKSPACE);
			if (!containerMapStr) {
				return null;
			}
			const containers = this._convertThreadDataFromStorage(containerMapStr) as ChatContainers;
			if (!containers || typeof containers !== 'object') {
				this._codeseekLogService.error('Invalid container structure in storage');
				return null;
			}

			if (!containers[CODESEEK_VIEW_CONTAINER_ID]) {
				containers[CODESEEK_VIEW_CONTAINER_ID] = {
					threadsState: { allThreads: {}, currentThreadId: '', currentThreadMessages: [] }
				};
			}

			for (const containerId of Object.keys(containers)) {
				if (!containers[containerId].threadsState) {
					containers[containerId].threadsState = {
						allThreads: {},
						currentThreadId: '',
						currentThreadMessages: []
					};
				} else {
					containers[containerId].threadsState.currentThreadMessages = [];
					if (!containers[containerId].threadsState.currentThreadId) {
						containers[containerId].threadsState.currentThreadId = '';
					}
				}
			}

			return containers;
		} catch (error) {
			this._codeseekLogService.error('Error reading containers:', error);
			return null;
		}
	}

	private _storeChatSummary() {
		const { allContainers } = this.containerState
		const serializedSummary = JSON.stringify(allContainers);
		this._storageService.store(
			THREAD_ABSTRACT_STORAGE_KEY,
			serializedSummary,
			StorageScope.WORKSPACE,
			StorageTarget.USER
		);
	}

	private _storeChatMessages(containerId: string, threadId: string, messags: ChatMessage[]) {
		const serializedMessages = JSON.stringify(messags)
		this._storageService.store(
			`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`,
			serializedMessages,
			StorageScope.WORKSPACE,
			StorageTarget.USER
		);
	}

	private _loadChatMessages(containerId: string, threadId: string): ChatMessage[] {
		const messagesStr = this._storageService.get(`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`, StorageScope.WORKSPACE);
		if (!messagesStr) {
			return [];
		}
		const messages = this._convertThreadDataFromStorage(messagesStr);

		if (messages && Array.isArray(messages)) {
			const firstUserMessage = messages.find(m => m.role === 'user');
			if (firstUserMessage && firstUserMessage.displayContent) {
				this._onDidSetChatTitle.fire({ containerId, message: firstUserMessage.displayContent });
			}
		}

		return messages as ChatMessage[];
	}

	private _deleteThreadMessages(containerId: string, threadId: string): void {
		this._storageService.remove(`${THREAD_MESSAGES_STORAGE_KEY}--${containerId}--${threadId}`, StorageScope.WORKSPACE)
	}

	// this should be the only place this.state = ... appears besides constructor
	private _setState(containerId: string, state: Partial<ThreadsState>, affectsCurrent: boolean) {
		this.containerState.allContainers[containerId].threadsState = {
			...this.containerState.allContainers[containerId].threadsState,
			...state
		};
		if (affectsCurrent)
			this._onDidChangeCurrentThread.fire(containerId);
	}

	public setNoStorageState(threadId: string, state: Partial<NoStorageState[string]>): void {
		this.noStorageState[threadId] = {
			...this.noStorageState[threadId],
			...state
		};
	}
	// private _getSelectionsUpToMessageIdx(containerId: string, messageIdx: number) {
	// 	const prevMessages = this.containerState.allContainers[containerId].threadsState.currentThreadMessages.slice(0, messageIdx);
	// 	return prevMessages.flatMap(m => m.role === 'user' && m.state.stagingSelections || []);
	// }

	public setStreamState(containerId: string, threadId: string, state: Partial<NonNullable<ThreadStreamState[string][string]>>) {
		this.streamState[containerId] ??= {};
		this.streamState[containerId][threadId] = {
			...this.streamState[containerId]?.[threadId],
			...state
		};
		this._onDidChangeStreamState.fire({ containerId, threadId });
	}

	// 判断是否应该影响当前线程的UI更新
	private shouldAffectCurrentThreadUI(containerId: string, threadId: string): boolean {
		const currentContainerId = this.containerState.currentContainerId;
		const currentThreadId = this.getCurrentThreadId(containerId);
		return containerId === currentContainerId && threadId === currentThreadId;
	}

	// ---------- streaming ----------
	finishStreamingTextMessage(containerId: string, threadId: string, content: string, error?: { message: string; fullError: Error | null; isExceededMaxLoopCount?: boolean }, affectCurrentThread: boolean = true) {
		// add assistant's message to chat history, and clear selection
		const chatId = this.getCurrentThreadChatId(threadId);
		const shouldAffectCurrentThread = affectCurrentThread && this.shouldAffectCurrentThreadUI(containerId, threadId);
		this.addMessageToThread(containerId, threadId, { role: 'assistant', content, displayContent: content || null, chatId }, shouldAffectCurrentThread);
		this.chatEnd(containerId, threadId, error);
	}

	chatEnd(containerId: string, threadId: string, error?: { message: string; fullError: Error | null; isExceededMaxLoopCount?: boolean }) {
		this.setStreamState(containerId, threadId, { messageSoFar: undefined, streamingToken: undefined, error, isStreaming: false, isLoading: false });
		const chatId: string = this.getCurrentThreadChatId(threadId);
		const sessionId: string = threadId;
		const modelSelection = this._codeseekSettingsService.getModelSelectionForContainer(FeatureNames.CtrlL, containerId);
		const modelName = modelSelection?.modelName ?? '';
		const metricsData = {
			eventContent: { modelName },
			traceIdPair: { traceId: chatId, parentTraceId: sessionId, sessionId },
		}

		const metrics_event = this.getCurrentThreadBusinessEvent(threadId);
		this._metricsService.capture(metrics_event, metricsData)
	}

	async editUserMessageAndStreamResponse({ containerId, threadId, userMessage, chatMode, messageIdx }: { containerId: string; threadId: string; userMessage: string; chatMode: ChatMode; messageIdx: number }) {

		const currentThreadMessages = this.containerState.allContainers[containerId].threadsState.currentThreadMessages

		if (currentThreadMessages[messageIdx]?.role !== 'user') {
			throw new Error("Error: editing a message with role !=='user'");
		}

		// get curr selections before clearing the message
		const currSelns = currentThreadMessages[messageIdx].state.stagingSelections || [];

		// clear messages up to the index
		const slicedMessages = currentThreadMessages.slice(0, messageIdx);
		this._setState(containerId, {
			currentThreadMessages: slicedMessages
		}, true);

		// re-add the message and stream it
		const businessEvent = chatMode == ChatMode.Agent ? METRICS_EVENT.AGENT : METRICS_EVENT.CHAT;
		if (chatMode === ChatMode.Agent) {
			this.addUserMessageAndStreamResponseWithAgent({ containerId, userMessageOpts: { from: 'Chat', chatMode, userMessage, selections: [...currSelns], businessEvent } });
		} else {
			this.addUserMessageAndStreamResponse({ containerId, userMessageOpts: { from: 'Chat', chatMode, userMessage, selections: [...currSelns], businessEvent } });
		}
	}

	async addUserMessageAndStreamResponse({ containerId, userMessageOpts, isFromAgent = false }: {
		containerId: string;
		userMessageOpts: userMessageOpts;
		isFromAgent?: boolean;
	}) {
		const thread = this.getCurrentThread(containerId);;
		const sessionId = thread.id;
		const threadId = thread.id;
		this.setNoStorageState(threadId, { businessEvent: userMessageOpts.businessEvent })
		this.updateThreadChatId(threadId);
		const currSelns: StagingSelectionItem[] = userMessageOpts.selections ?? thread.state.stateSelections.list;
		const state = {
			stagingSelections: [...currSelns],
			isBeingEdited: false,
		};

		if (userMessageOpts.userMessage && thread.firstUserMessage === '') {
			this._onDidSetChatTitle.fire({ containerId, message: userMessageOpts.userMessage });
		}

		// add user's message to chat history
		const content = await chat_userMessageContent(
			userMessageOpts,
			currSelns,
			this._codeseekFileService,
			this._codeSeekExporerService,
			this._modelService,
			this._workspaceContextService,
			this._codeseekSettingsService,
			this._urlContentFetcher,
			this._notificationService,
			this._zteUserInfoService,
		);

		const userHistoryElt: ChatMessage = { role: 'user', content, displayContent: userMessageOpts.userMessage, state };
		this._setFirstUserMessage(containerId, threadId, userMessageOpts.userMessage)
		if (!isFromAgent) {
			this.addMessageToThread(containerId, threadId, userHistoryElt);
		}

		this.setStreamState(containerId, threadId, { streamingToken: undefined, isStreaming: true, isLoading: true, error: undefined });
		const mentionCodebase = currSelns.some(s => s.type === 'Codebase');
		let codebaseSelections: CodebaseSelection[] = [];
		let sels: StagingSelectionItem[];
		if (mentionCodebase) {
			codebaseSelections = await this.getCodeBaseSelections(containerId, userMessageOpts);
			sels = [...currSelns.filter(item => item.type !== 'Codebase'), ...codebaseSelections];
		} else {
			sels = [...currSelns]
		}

		const userMessageContent = await chat_userMessageContent(
			userMessageOpts,
			sels,
			this._codeseekFileService,
			this._codeSeekExporerService,
			this._modelService,
			this._workspaceContextService,
			this._codeseekSettingsService,
			this._urlContentFetcher,
			this._notificationService,
			this._zteUserInfoService,
		);
		const user = await this._zteUserInfoService.getUserInfo();
		const userId = user?.userId ?? '';

		// agent loop
		const agentLoop = async () => {

			let shouldSendAnotherMessage = true;
			let nMessagesSent = 0;

			while (shouldSendAnotherMessage) {
				shouldSendAnotherMessage = false;
				nMessagesSent += 1;

				let res_: () => void;
				const awaitable = new Promise<void>((res, rej) => { res_ = res; });

				// replace last userMessage with userMessageFullContent (which contains all the files too)
				const modelSelection = this._codeseekSettingsService.getModelSelectionForContainer(FeatureNames.CtrlL, containerId);
				let toLLMChatMessage_: (c: ChatMessage) => LLMChatMessage;
				if (modelSelection?.modelName === 'AI-IDE-Chat') {
					const agentManageService = this._instantiationService.invokeFunction(accessor => accessor.get(IAgentManageService));
					toLLMChatMessage_ = agentManageService.toLLMChatMessage;
				} else {
					toLLMChatMessage_ = toLLMChatMessage;
				}
				const messages_ = this.containerState.allContainers[containerId].threadsState.currentThreadMessages.map(m => (toLLMChatMessage_(m)));
				const lastUserMsgIdx = findLastIndex(messages_, m => m.role === 'user');
				const modelName = modelSelection?.modelName ?? '';
				let messages = messages_;
				if (lastUserMsgIdx !== -1) { // should never be -1
					messages = [
						...messages.slice(0, lastUserMsgIdx),
						{ role: 'user', content: userMessageContent },
						...messages.slice(lastUserMsgIdx + 1, Infinity)];
				}
				const chatMessageEmitters_ = (): SendLLMType => {
					let systemMessageContent = '';
					if (userMessageOpts.from === 'Fix') {
						systemMessageContent = fix_systemMessage(modelName);
					} else {
						systemMessageContent = chat_systemMessage(modelName);
					}
					return {
						messagesType: 'chatMessages',
						messages: [
							{ role: 'system', content: systemMessageContent },
							{ role: 'user', content: user_rules(this._codeseekSettingsService.state.globalSettings.userRules) },
							...messages,
						],
					};
				};
				const chatMessageEmitters = chatMessageEmitters_();
				this._codeseekLogService.info('Prompt:', JSON.stringify(chatMessageEmitters));
				const startTime = Date.now();
				let firstTokenTime: number | null = null;
				let response = '';
				const reporter = (sessionId: string, error?: any) => {
					const firstTokenCostTime = firstTokenTime ? firstTokenTime - startTime : 0;
					const totalCostTime = Date.now() - startTime;
					const request = {
						...chatMessageEmitters,
						useProviderFor: FeatureNames.CtrlL,
					};
					const metricsData = {
						eventContent: { modelName, firstTokenCostTime, totalCostTime, request, response, error },
						traceIdPair: { traceId: sessionId, parentTraceId: '', sessionId },
					}
					if (userMessageOpts.chatMode === ChatMode.Ask) {
						this._metricsService.capture(METRICS_EVENT.CHAT, metricsData);
					} else if (userMessageOpts.chatMode === ChatMode.Agent) {
						this._metricsService.capture(METRICS_EVENT.AGENT, metricsData);
					}
				}
				const llmCancelToken = this._llmMessageService.sendLLMMessage({
					containerId,
					userId,
					...chatMessageEmitters,
					useProviderFor: FeatureNames.CtrlL,
					logging: { loggingName: userMessageOpts.chatMode },
					onText: ({ fullText }) => {
						firstTokenTime = firstTokenTime ?? Date.now();
						fullText = removeThinkTag(fullText);
						this.setStreamState(containerId, threadId, { messageSoFar: fullText });
					},
					onFinalMessage: async ({ fullText, toolCalls }) => {
						this._codeseekLogService.info(`Final message, threadId: ${threadId}, fullText: `,
							fullText, `toolCalls: `, JSON.stringify(toolCalls));
						fullText = removeCloseThinkTag(fullText);
						response = fullText;
						reporter(sessionId);
						if ((toolCalls?.length ?? 0) === 0) {
							this.setStreamState(containerId, threadId, { messageSoFar: undefined, tool: undefined });
							this.finishStreamingTextMessage(containerId, threadId, fullText);
						}
						res_();
					},
					onError: (error) => {
						this.finishStreamingTextMessage(containerId, threadId, this.streamState[containerId]?.[threadId]?.messageSoFar ?? '', error);
						res_();
						reporter(sessionId, error);
					},
				});
				if (llmCancelToken === null) break;
				this.setStreamState(containerId, threadId, { streamingToken: llmCancelToken, isStreaming: true, isLoading: true });

				await awaitable;
			}
		};

		agentLoop(); // DO NOT AWAIT THIS, this fn should resolve when ready to clear inputs

	}

	private async getCodeBaseSelections(containerId: string, userMessageOpts: userMessageOpts): Promise<CodebaseSelection[]> {
		if (userMessageOpts.from === 'Fix') {
			return [];
		}
		let expandedUserMessage = [userMessageOpts.userMessage];
		if (this._codeseekSettingsService.state.globalSettings.enableCodeBase) {
			// expandedUserMessage = await this.expandUserMessage(userMessageOpts.userMessage, this.getCurrentThread().id);
			this.setStreamState(containerId, this.getCurrentThread(containerId).id, { streamingToken: undefined, isStreaming: true });
			const searchResult = await this._codebaseSearchService.semanticSearch(expandedUserMessage.join('\n'));
			this._codeseekLogService.info('Codebase search result:',
				JSON.stringify(searchResult, null, 2), `query: ${expandedUserMessage}`);
			const codebaseSelections: CodebaseSelection[] = searchResult.map(r => ({
				type: 'Codebase' as const,
				fileURI: r.uri,
				title: 'Codebase',
				selectionStr: r.content,
				range: r.range,
				fromMention: false
			}));
			return codebaseSelections;
		}
		return [];
	}


	async addUserMessageAndStreamResponseWithAgent({ containerId, userMessageOpts }: {
		containerId: string;
		userMessageOpts: userMessageOpts;
	}) {
		this._codeseekLogService.info('[AgentStart]')
		this._codeseekLogService.info('[Agent] userMessageOpts:', JSON.stringify(userMessageOpts));
		const thread = this.getCurrentThread(containerId);
		const threadId = thread.id;

		this.setNoStorageState(threadId, { businessEvent: userMessageOpts.businessEvent })

		userMessageOpts.selections = userMessageOpts.selections || thread.state.stateSelections.list;
		const state = {
			stagingSelections: [...userMessageOpts.selections],
			isBeingEdited: false,
		};

		if (userMessageOpts.userMessage && thread.firstUserMessage === '') {
			this._onDidSetChatTitle.fire({ containerId, message: userMessageOpts.userMessage });
		}

		const userHistoryElt: ChatMessage = { role: 'user', content: userMessageOpts.userMessage, displayContent: userMessageOpts.userMessage, state };
		this._setFirstUserMessage(containerId, threadId, userMessageOpts.userMessage)
		this.addMessageToThread(containerId, threadId, userHistoryElt);
		this.setStreamState(containerId, threadId, { error: undefined, isStreaming: true, isLoading: true });
		const workspaceInfo = await this.getWorkspaceInfo(containerId);
		this._codeseekLogService.info('[Agent] workspaceInfo:', JSON.stringify(workspaceInfo));
		userMessageOpts.workspaceInfo = workspaceInfo;
		const chatId = this.updateThreadChatId(threadId);
		this._instantiationService.invokeFunction(accessor => {
			const agentManageService = accessor.get(IAgentManageService);
			agentManageService.agentLoop(containerId, threadId, chatId, userMessageOpts);
		});
	}

	cancelStreaming(containerId: string, threadId: string) {
		const cancelToken = this.streamState[containerId]?.[threadId]?.streamingToken;
		if (cancelToken !== undefined) {
			this._instantiationService.invokeFunction(accessor => {
				const agentManageService = accessor.get(IAgentManageService);
				agentManageService.abort(cancelToken)
			});
		}
		this.finishStreamingTextMessage(containerId, threadId, this.streamState[containerId]?.[threadId]?.messageSoFar ?? '');

		this.setNoStorageState(threadId, { askMessage: undefined, askResponse: undefined, askResponseText: undefined })
	}

	dismissStreamError(containerId: string, threadId: string): void {
		this.setStreamState(containerId, threadId, { error: undefined });
	}

	private async getWorkspaceInfo(containerId: string): Promise<WorkspaceInfo> {
		const editorIdentifiers = this._editorService.getEditors(EditorsOrder.SEQUENTIAL);
		const repository = this._contextKeyService.getContextKeyValue<string>('scmActiveRepositoryName') || '';
		const branch = this._contextKeyService.getContextKeyValue<string>('scmActiveRepositoryBranchName') || '';

		const workingDirectory = getWorkspaceUri(this._workspaceContextService).workspaceUri?.fsPath ?? '';
		let workingDirectoryDetail;
		if ((workingDirectory != '')) {
			if ((workingDirectory in this.workspaceDirectoryTree)) {
				workingDirectoryDetail = this.workspaceDirectoryTree[workingDirectory];
			} else {
				const cutOffMessage = `...Directories string cut off, use tools to read more...`;
				workingDirectoryDetail = await this._directoryStrService.getAllDirectoriesStr({ cutOffMessage });
				this.workspaceDirectoryTree[workingDirectory] = workingDirectoryDetail;
			}
		}
		const relativePaths = editorIdentifiers
			.filter(editor => editor.editor.resource !== undefined)
			.map(editor => editor.editor.resource?.fsPath)
			.filter(path => path !== undefined)
			.map(path => getRelativePath(this._workspaceContextService, path));
		const openTabs = relativePaths.join('\n');
		const rules = this._codeseekSettingsService.state.globalSettings.userRules;
		const user = await this._zteUserInfoService.getUserInfo();

		return {
			repository,
			branch,
			operatingSystem: osType,
			defaultShell: this.getShell() ?? '',
			currentWorkingDirectory: workingDirectory,
			workingDirectoryDetail: workingDirectoryDetail ?? '',
			openTabs,
			rules,
			userId: user?.userId ?? ''
		};
	}

	private getShell() {
		let defaultProfileName = null;
		let profiles = null;

		if (isWindows) {
			defaultProfileName = this._configurationService.getValue<string>("terminal.integrated.defaultProfile.windows");
			profiles = this._configurationService.getValue<Record<string, TerminalProfile>>("terminal.integrated.profiles.windows") || {};
		} else if (isMacintosh) {
			defaultProfileName = this._configurationService.getValue<string>("terminal.integrated.defaultProfile.osx");
			profiles = this._configurationService.getValue<Record<string, TerminalProfile>>("terminal.integrated.profiles.osx") || {};
		} else if (isLinux) {
			defaultProfileName = this._configurationService.getValue<string>("terminal.integrated.defaultProfile.linux");
			profiles = this._configurationService.getValue<Record<string, TerminalProfile>>("terminal.integrated.profiles.linux") || {};
		}

		if (!defaultProfileName) {
			return null;
		}
		const profile = profiles?.[defaultProfileName];
		if (isWindows) {
			if (defaultProfileName.toLowerCase().includes("powershell")) {
				if (profile?.path) {
					return profile.path;
				} else if (profile?.source === "PowerShell") {
					return SHELL_PATHS.POWERSHELL_7;
				}
				return SHELL_PATHS.POWERSHELL_LEGACY;
			}
			if (profile?.path) {
				return profile.path;
			}
			if (profile?.source === "WSL" || defaultProfileName.toLowerCase().includes("wsl")) {
				return SHELL_PATHS.WSL_BASH;
			}
			return SHELL_PATHS.CMD;
		}
		return profile?.path || null;
	}


	// ---------- the rest ----------

	isCurrentThreadWorking(containerId: string): boolean {
		const currentThread = this.getCurrentThread(containerId);
		if (!currentThread) return false;
		const streamState = this.streamState[containerId]?.[currentThread.id];
		return streamState && streamState.streamingToken && !streamState.error ? true : false;
	}

	getCurrentContainerId(): string {
		return this.containerState.currentContainerId;
	}

	getCurrentThreadId(containerId: string): string {
		return this.containerState.allContainers[containerId].threadsState.currentThreadId;
	}

	getCurrentThread(containerId: string): ChatThreads[string] {
		const container = this.containerState.allContainers[containerId];
		if (!container.threadsState.currentThreadId || container.threadsState.currentThreadId === '') {
			return undefined as unknown as ChatThreads[string];
		}
		return container.threadsState.allThreads[container.threadsState.currentThreadId];
	}

	getCurrentThreadMessages(containerId: string): ChatMessage[] {
		return this.containerState.allContainers[containerId].threadsState.currentThreadMessages
	}

	isFocusingContainer(containerId: string): boolean {
		return this.containerState.currentContainerId === containerId;
	}

	getFocusedMessageIdx(containerId: string): number | undefined {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return undefined;

		// get the focusedMessageIdx
		const focusedMessageIdx = thread.state.focusedMessageIdx;
		if (focusedMessageIdx === undefined) return;

		// check that the message is actually being edited
		const focusedMessage = this.getCurrentThreadMessages(containerId)[focusedMessageIdx];
		if (focusedMessage.role !== 'user') return;
		if (!focusedMessage.state) return;

		return focusedMessageIdx;
	}

	isFocusingMessage(containerId: string): boolean {
		return this.getFocusedMessageIdx(containerId) !== undefined;
	}

	switchToContainer(containerId: string): void {
		this.containerState.currentContainerId = containerId;
		this._sidebarStateService.fireFocusContainer(containerId);
		this._onDidChangeCurrentContainer.fire();
		this._onDidChangeCurrentThread.fire(containerId);
	}

	switchToThread(containerId: string, threadId: string, targetContainerId?: string) {
		const actualTargetContainerId = targetContainerId ?? containerId;

		if (!this.containerState.allContainers[actualTargetContainerId]) {
			return;
		}
		const messages = this._loadChatMessages(containerId, threadId);
		if (actualTargetContainerId !== this.containerState.currentContainerId) {
			this.switchToContainer(actualTargetContainerId);
		}
		this._setState(actualTargetContainerId, {
			currentThreadId: threadId,
			currentThreadMessages: messages
		}, true);
	}

	deleteContainer(containerId: string): void {
		if (containerId === CODESEEK_VIEW_CONTAINER_ID) {
			return;
		}

		const currentThreadId = this.getCurrentThreadId(containerId);
		if (currentThreadId) {
			this.cancelStreaming(containerId, currentThreadId);
		}

		const threadsState = this.containerState.allContainers[containerId]?.threadsState;
		if (threadsState) {
			const threadIds = Object.keys(threadsState.allThreads);
			threadIds.forEach(threadId => {
				this._deleteThreadMessages(containerId, threadId);
			});
		}

		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewContainer = viewContainersRegistry.get(containerId);
		if (viewContainer) {
			viewContainersRegistry.deregisterViewContainer(viewContainer);
		}

		if (this.streamState[containerId]) {
			delete this.streamState[containerId];
		}

		delete this.containerState.allContainers[containerId];

		if (this.containerState.currentContainerId === containerId) {
			this.containerState.currentContainerId = CODESEEK_VIEW_CONTAINER_ID;
			this._sidebarStateService.fireFocusContainer(CODESEEK_VIEW_CONTAINER_ID);
			this._onDidChangeCurrentContainer.fire();
			this._onDidChangeCurrentThread.fire(CODESEEK_VIEW_CONTAINER_ID);
		}

		this._storeChatSummary();
	}

	deleteThread(containerId: string, threadId: string) {
		const currentThread = this.getCurrentThread(containerId);
		const isCurrentThread = currentThread?.id === threadId;

		if (isCurrentThread) {
			this.cancelStreaming(containerId, threadId);
		}

		const { allThreads } = this.containerState.allContainers[containerId].threadsState;
		delete allThreads[threadId];
		this._deleteThreadMessages(containerId, threadId)

		// 清理streamState中的相关状态
		if (this.streamState[containerId]?.[threadId]) {
			delete this.streamState[containerId][threadId];
		}

		// 保存更新后的线程集合
		this._storeChatSummary();

		if (isCurrentThread) {
			const remainingThreadIds = Object.keys(allThreads);
			if (remainingThreadIds.length > 0) {
				// 切换到第一个可用的线程
				this.switchToThread(containerId, remainingThreadIds[0]);
			} else {
				// 如果没有可用的线程，创建一个新线程
				this.openNewThread(containerId);
			}
		} else {
			this._setState(containerId, { allThreads }, true);
		}
	}

	private _addSelectionToChat(containerId: string) {
		const selection = this._codeSeekCodeSelectionService.getFileSelction();

		if (selection && selection.type === 'File' && this.isFileApplying(selection.fileURI.toString())) {
			return;
		}

		this.addSelectionToChat(containerId, selection);
	}

	addSelectionToChat(containerId: string, selection?: StagingSelectionItem) {
		if (!selection) return;

		const focusedMessageIdx = this.getFocusedMessageIdx(containerId);
		let selections: StagingSelectionItem[] = [];
		let setSelections = (s: StagingSelectionItem[]) => { };

		if (focusedMessageIdx === undefined) {
			selections = this.getCurrentThreadStagingSelections(containerId);
			setSelections = (s: StagingSelectionItem[]) => this.setCurrentThreadStagingSelections(containerId, s);
		} else {
			selections = this.getCurrentMessageState(containerId, focusedMessageIdx).stagingSelections;
			setSelections = (s) => this.setCurrentMessageState(containerId, focusedMessageIdx, { stagingSelections: s });
		}

		const updatedSelections = selections.map(item => {
			if (item.type === 'File' && selection.type === 'File' &&
				item.fileURI.toString() === selection.fileURI.toString() &&
				item.fromActive) {
				return {
					...item,
					fromActive: false,
					fromEditor: true
				};
			}
			return item;
		});

		setSelections(this._mentionsService.addItemToSelectedFile(updatedSelections, selection));
	}

	addMessageToThread(containerId: string, threadId: string, message: ChatMessage, affectCurrentThread: boolean = true) {
		const { allThreads } = this.containerState.allContainers[containerId].threadsState;

		const oldThread = allThreads[threadId];
		if (!oldThread) {
			return; // 如果线程不存在，不添加消息
		}

		const currentThreadMessages = this.getCurrentThreadMessages(containerId);
		const newMessages = [...currentThreadMessages, message]

		// update state and store it
		const newThreads = {
			...allThreads,
			[oldThread.id]: {
				...oldThread,
				lastModified: new Date().toISOString(),
				messagesLength: newMessages.length,
			}
		};

		// 只有当需要影响当前线程的UI时才更新currentThreadMessages
		if (affectCurrentThread && this.shouldAffectCurrentThreadUI(containerId, threadId)) {
			this._setState(containerId, { allThreads: newThreads, currentThreadMessages: newMessages }, true);
		} else {
			this._setState(containerId, { allThreads: newThreads }, false);
		}

		this._storeChatSummary();
		this._storeChatMessages(containerId, oldThread.id, newMessages);
	}

	private _setFirstUserMessage(containerId: string, threadId: string, userMessage: string) {
		const allThreads = this.containerState.allContainers[containerId].threadsState.allThreads
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId]
		if (thread.firstUserMessage === '') {
			const newThreads = {
				...allThreads,
				[thread.id]: {
					...thread,
					firstUserMessage: userMessage
				}
			}
			this._setState(containerId, { allThreads: newThreads }, false)
		}
	}
	// sets the currently selected message (must be undefined if no message is selected)
	setFocusedMessageIdx(containerId: string, messageIdx: number | undefined) {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			allThreads: {
				...this.containerState.allContainers[containerId].threadsState.allThreads,
				[threadId]: {
					...thread,
					state: {
						...thread.state,
						focusedMessageIdx: messageIdx,
					}
				}
			}
		}, true);
	}

	// set message.state
	private _setCurrentMessageState(containerId: string, state: Partial<UserMessageState>, messageIdx: number): void {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			currentThreadMessages: this.containerState.allContainers[containerId].threadsState.currentThreadMessages.map((m, i) =>
				i === messageIdx && m.role === 'user' ? {
					...m,
					state: {
						...m.state,
						...state
					},
				} : m
			)
		}, true);

	}

	// set thread.state
	private _setCurrentThreadState(containerId: string, state: Partial<ThreadType['state']>): void {

		const threadId = this.containerState.allContainers[containerId].threadsState.currentThreadId;
		const thread = this.containerState.allContainers[containerId].threadsState.allThreads[threadId];
		if (!thread) return;

		this._setState(containerId, {
			allThreads: {
				...this.containerState.allContainers[containerId].threadsState.allThreads,
				[thread.id]: {
					...thread,
					state: {
						...thread.state,
						...state
					}
				}
			}
		}, true);

	}

	getCurrentThreadStagingSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return [];
		return thread.state.stateSelections.list;
	};

	getCurrentThreadStateSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return { list: [], followEditorActive: true };
		return thread.state.stateSelections;
	};

	setCurrentThreadStagingSelections = (containerId: string, stagingSelections: StagingSelectionItem[]) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return;
		const stateSelections = thread.state.stateSelections;
		stateSelections.list = [...stagingSelections];
		this._setCurrentThreadState(containerId, { stateSelections });
	};

	// gets `staging` and `setStaging` of the currently focused element, given the index of the currently selected message (or undefined if no message is selected)

	getCurrentMessageState = (containerId: string, messageIdx: number): UserMessageState => {
		const currMessage = this.containerState.allContainers[containerId].threadsState.currentThreadMessages[messageIdx];
		if (!currMessage || currMessage.role !== 'user') return defaultMessageState;
		return currMessage.state;
	}

	setCurrentMessageState = (containerId: string, messageIdx: number, newState: Partial<UserMessageState>) => {
		const currMessage = this.containerState.allContainers[containerId].threadsState.currentThreadMessages[messageIdx];
		if (!currMessage || currMessage.role !== 'user') return;
		this._setCurrentMessageState(containerId, newState, messageIdx);
	}

	setCurrentThreadStateSelectionsChangeSelections = (containerId: string) => {
		const thread = this.getCurrentThread(containerId);
		if (!thread) return;
		const stagingSelections = thread.state.stateSelections;
		stagingSelections.followEditorActive = false;
	}

	private onDidActiveEditorChange(containerId: string) {
		// 获取当前活动编辑器的URI
		const activeEditor = this._editorService.activeEditor;
		const activeFileUri = activeEditor?.resource?.toString();

		// 检查当前文件是否处于Apply操作状态
		if (activeFileUri && this.isFileApplying(activeFileUri)) {
			// 如果文件正在进行Apply操作，跳过自动添加到聊天上下文
			return;
		}

		const stateSelections = this.getCurrentThreadStateSelections(containerId);

		// 如果followEditorActive为false，说明用户已经手动修改过选择，不再自动跟随编辑器
		if (!stateSelections.followEditorActive) {
			return;
		}

		const selections = this.getCurrentThreadStagingSelections(containerId);
		if (selections.length === 0) {
			this._addSelectionToChat(containerId);
		} else {
			if (this.containerState.allContainers[containerId].threadsState.currentThreadMessages.length === 0) {
				const selection = this._codeSeekCodeSelectionService.getFileSelction();
				if (!selection) {
					return;
				}

				if (selection.type === 'File' && this.isFileApplying(selection.fileURI.toString())) {
					return;
				}

				let hasSameUriFromEditor = false;
				const filteredSelections = selections.filter(s => {
					if (s.type === 'File') {
						if (s.fileURI.toString() === selection.fileURI.toString() && s.fromEditor) {
							hasSameUriFromEditor = true;
							return true;
						}
						return !s.fromActive;
					}
					return true;
				});
				if (!hasSameUriFromEditor) {
					this.setCurrentThreadStagingSelections(containerId, [selection, ...filteredSelections]);
				} else {
					this.setCurrentThreadStagingSelections(containerId, filteredSelections);
				}
			}
		}
	}

	setApplyingState(fileUri: string, isApplying: boolean, containerId?: string): void {
		if (isApplying) {
			// 开始Apply操作时，保存当前选择状态
			const currentSelections = containerId ? this.getCurrentThreadStagingSelections(containerId) : undefined;
			this.applyingFileState.set(fileUri, {
				isApplying: true,
				applyStartTime: Date.now(),
				originalSelections: currentSelections ? [...currentSelections] : undefined
			});
		} else {
			// 结束Apply操作时，清理状态并设置超时清理机制
			const state = this.applyingFileState.get(fileUri);
			if (state) {
				setTimeout(() => {
					this.applyingFileState.delete(fileUri);
				}, 1000); // 1秒延迟确保Apply操作完全结束
			}
		}
	}

	getApplyingState(fileUri: string): ApplyingFileState | undefined {
		return this.applyingFileState.get(fileUri);
	}

	private isFileApplying(fileUri: string): boolean {
		const state = this.applyingFileState.get(fileUri);
		if (!state) return false;

		// 检查Apply操作是否超时（超过30秒认为异常，自动清理）
		const now = Date.now();
		if (now - state.applyStartTime > 30000) {
			this.applyingFileState.delete(fileUri);
			return false;
		}

		return state.isApplying;
	}

	setAskResponse(threadId: string, askResponse: AskResponse): void {
		this.setNoStorageState(threadId, { askResponse })
	}

	private _registerExistingContainers(): void {
		const viewContainersRegistry = Registry.as<IViewContainersRegistry>(ViewExtensions.ViewContainersRegistry);
		const viewsRegistry = Registry.as<IViewsRegistry>(ViewExtensions.ViewsRegistry);

		const containerIds = Object.keys(this.containerState.allContainers);

		for (const containerId of containerIds) {
			if (containerId === CODESEEK_VIEW_CONTAINER_ID) {
				continue;
			}

			const orderStr = containerId.split('.')[3];
			const orderNum = parseInt(orderStr);
			if (isNaN(orderNum)) {
				continue;
			}

			const existingContainer = viewContainersRegistry.get(containerId);
			if (existingContainer) {
				continue;
			}

			const newContainer = viewContainersRegistry.registerViewContainer({
				id: containerId,
				title: localize2('newCodeseekContainer', 'New Chat'),
				ctorDescriptor: new SyncDescriptor(
					ViewPaneContainer,
					[containerId, {
						mergeViewWithContainerWhenSingleView: true,
						orientation: Orientation.HORIZONTAL,
					}]
				),
				hideIfEmpty: false,
				order: orderNum,
			}, ViewContainerLocation.AuxiliaryBar, { doNotRegisterOpenCommand: true, isDefault: true });

			viewsRegistry.registerViews([{
				id: containerId,
				hideByDefault: false,
				name: localize2('newCodeseekChat', 'New Chat'),
				ctorDescriptor: new SyncDescriptor(SidebarViewPane),
				canToggleVisibility: false,
				canMoveView: false,
				weight: 80,
				order: 1,
			}], newContainer);

			const threadsState = this.containerState.allContainers[containerId].threadsState;
			const threadIds = Object.keys(threadsState.allThreads);

			if (threadIds.length > 0 && (!threadsState.currentThreadId || threadsState.currentThreadId === '')) {
				const firstThreadId = threadIds[0];
				const messages = this._loadChatMessages(containerId, firstThreadId);
				this._setState(containerId, {
					currentThreadId: firstThreadId,
					currentThreadMessages: messages
				}, false);
			} else if (threadIds.length === 0) {
				this.openNewThread(containerId);
			}
		}

		const maxOrder = containerIds.reduce((max, id) => {
			const orderStr = id.split('.')[3];
			const orderNum = parseInt(orderStr);
			return isNaN(orderNum) ? max : Math.max(max, orderNum);
		}, order);

		order = Math.max(order, maxOrder + 1);
	}

	cancelExecTool(threadId: string): void {
		const cancelToken = this.threadId2CancelTokens.get(threadId);
		if (cancelToken) {
			cancelToken.cancel();
		}
	}

	switchToAgent(containerId: string, threadId: string): void {
		this._codeseekSettingsService.setChatMode(ChatMode.Agent, { containerId });
		this._setState(containerId, { currentThreadId: threadId }, true);
		this._onDidChangeCurrentThread.fire(containerId);
	}

	switchToAsk(containerId: string, threadId: string): void {
		this._codeseekSettingsService.setChatMode(ChatMode.Ask, { containerId });
		this._setState(containerId, { currentThreadId: threadId }, true);
		this._onDidChangeCurrentThread.fire(containerId);
	}

	public getCurrentThreadChatId(threadId: string): string {
		return this.threadId2ChatIds.get(threadId) || '';
	}

	private updateThreadChatId(threadId: string): string {
		const chatId = generateUuid();
		this.threadId2ChatIds.set(threadId, chatId);
		return chatId;
	}

	getCurrentThreadBusinessEvent(threadId: string) {
		return this.noStorageState[threadId]?.businessEvent || '';
	}

	getNoStorageState(threadId: string): NoStorageState[string] | undefined {
		return this.noStorageState[threadId]
	}

	updateToolCall(containerId: string, threadId: string, state: Partial<ToolCallState>): void {
		if (!this.streamState[containerId] || !this.streamState[containerId][threadId]) {
			return;
		}
		const currentTool = this.streamState[containerId][threadId]?.tool;
		this.setStreamState(containerId, threadId, {
			tool: {
				toolCall: {
					...currentTool?.toolCall,
					...state.toolCall,
				} as ToolCallType,
				toolCallResult: {
					...currentTool?.toolCallResult,
					...state.toolCallResult,
				} as ToolCallResultType,
			},
		});
	}
}

registerSingleton(IChatThreadService, ChatThreadService, InstantiationType.Eager);
