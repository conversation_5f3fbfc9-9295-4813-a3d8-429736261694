{"name": "flow", "productionName": "Flow", "productVersion": "1.0.2", "version": "1.97.2", "codebaseVersion": "1.3.12", "codebaseUrl": "https://artxa.zte.com.cn/artifactory/rjgctd-local-release-generic/codebase", "distro": "a9c92d9048a3323ec0c54285b198e2b9fb4e8853", "author": {"name": "ZTE Corporation", "email": "<EMAIL>"}, "license": "MIT", "main": "./out/main.js", "type": "module", "private": true, "scripts": {"buildreact": "cd ./src/vs/workbench/contrib/codeseek/browser/react/ && node build.js && cd ../../../../../../../", "watchreact": "cd ./src/vs/workbench/contrib/codeseek/browser/react/ && node build.js --watch && cd ../../../../../../../", "watchreactd": "deemon npm run watchreact", "test": "echo Please run any of the test scripts from the scripts folder.", "test-browser": "npx playwright install && node test/unit/browser/index.js", "test-browser-no-install": "node test/unit/browser/index.js", "test-node": "mocha test/unit/node/index.js --delay --ui=tdd --timeout=5000 --exit", "test-extension": "vscode-test", "preinstall": "node build/npm/preinstall.js", "postinstall": "node build/npm/postinstall.js", "install-git-hooks": "node scripts/install-git-hooks.js", "compile": "npm run buildreact && node ./node_modules/gulp/bin/gulp.js compile", "watch": "npm-run-all -lp watchreact watch-client watch-extensions", "watchd": "deemon npm run watch", "watch-webd": "deemon npm run watch-web", "kill-watchd": "deemon --kill npm run watch", "kill-watch-webd": "deemon --kill npm run watch-web", "restart-watchd": "deemon --restart npm run watch", "restart-watch-webd": "deemon --restart npm run watch-web", "watch-client": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js watch-client", "watch-clientd": "deemon npm run watch-client", "kill-watch-clientd": "deemon --kill npm run watch-client", "watch-extensions": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js watch-extensions watch-extension-media", "watch-extensionsd": "deemon npm run watch-extensions", "kill-watch-extensionsd": "deemon --kill npm run watch-extensions", "precommit": "node build/hygiene.js", "gulp": "node --max-old-space-size=8192 ./node_modules/gulp/bin/gulp.js", "electron": "node build/lib/electron", "7z": "7z", "update-grammars": "node build/npm/update-all-grammars.mjs", "update-localization-extension": "node build/npm/update-localization-extension.js", "smoketest": "node build/lib/preLaunch.js && cd test/smoke && npm run compile && node test/index.js", "smoketest-no-compile": "cd test/smoke && node test/index.js", "download-builtin-extensions": "node build/lib/builtInExtensions.js", "download-builtin-extensions-cg": "node build/lib/builtInExtensionsCG.js", "monaco-compile-check": "tsc -p src/tsconfig.monaco.json --noEmit", "tsec-compile-check": "node node_modules/tsec/bin/tsec -p src/tsconfig.tsec.json", "vscode-dts-compile-check": "tsc -p src/tsconfig.vscode-dts.json && tsc -p src/tsconfig.vscode-proposed-dts.json", "valid-layers-check": "node build/lib/layersChecker.js", "update-distro": "node build/npm/update-distro.mjs", "web": "echo 'npm run web' is replaced by './scripts/code-server' or './scripts/code-web'", "compile-cli": "gulp compile-cli", "compile-web": "node ./node_modules/gulp/bin/gulp.js compile-web", "watch-web": "node ./node_modules/gulp/bin/gulp.js watch-web", "watch-cli": "node ./node_modules/gulp/bin/gulp.js watch-cli", "eslint": "node build/eslint", "stylelint": "node build/stylelint", "playwright-install": "npm exec playwright install", "compile-build": "node ./node_modules/gulp/bin/gulp.js compile-build", "compile-extensions-build": "node ./node_modules/gulp/bin/gulp.js compile-extensions-build", "minify-vscode": "node ./node_modules/gulp/bin/gulp.js minify-vscode", "minify-vscode-reh": "node ./node_modules/gulp/bin/gulp.js minify-vscode-reh", "minify-vscode-reh-web": "node ./node_modules/gulp/bin/gulp.js minify-vscode-reh-web", "hygiene": "node ./node_modules/gulp/bin/gulp.js hygiene", "core-ci": "node ./node_modules/gulp/bin/gulp.js core-ci", "core-ci-pr": "node ./node_modules/gulp/bin/gulp.js core-ci-pr", "extensions-ci": "node ./node_modules/gulp/bin/gulp.js extensions-ci", "extensions-ci-pr": "node ./node_modules/gulp/bin/gulp.js extensions-ci-pr", "perf": "node scripts/code-perf.js", "update-build-ts-version": "npm install typescript@next && tsc -p ./build/tsconfig.build.json"}, "dependencies": {"@anthropic-ai/sdk": "^0.32.1", "@floating-ui/react": "^0.27.3", "@google/generative-ai": "^0.21.0", "@microsoft/1ds-core-js": "^3.2.13", "@microsoft/1ds-post-js": "^3.2.13", "@mistralai/mistralai": "^1.4.0", "@parcel/watcher": "2.5.0", "@rrweb/record": "^2.0.0-alpha.17", "@rrweb/types": "^2.0.0-alpha.17", "@types/semver": "^7.5.8", "@vscode/deviceid": "^0.1.1", "@vscode/iconv-lite-umd": "0.7.0", "@vscode/policy-watcher": "^1.1.8", "@vscode/proxy-agent": "^0.31.0", "@vscode/ripgrep": "1.15.10", "@vscode/spdlog": "^0.15.0", "@vscode/sqlite3": "5.1.8-vscode", "@vscode/sudo-prompt": "9.3.1", "@vscode/tree-sitter-wasm": "^0.0.5", "@vscode/vscode-languagedetection": "1.0.21", "@vscode/webview-ui-toolkit": "^1.4.0", "@vscode/windows-mutex": "^0.5.0", "@vscode/windows-process-tree": "^0.6.0", "@vscode/windows-registry": "^1.1.0", "@xterm/addon-clipboard": "^0.2.0-beta.80", "@xterm/addon-image": "^0.9.0-beta.97", "@xterm/addon-ligatures": "^0.10.0-beta.97", "@xterm/addon-progress": "^0.2.0-beta.3", "@xterm/addon-search": "^0.16.0-beta.97", "@xterm/addon-serialize": "^0.14.0-beta.97", "@xterm/addon-unicode11": "^0.9.0-beta.97", "@xterm/addon-webgl": "^0.19.0-beta.97", "@xterm/headless": "^5.6.0-beta.97", "@xterm/xterm": "^5.6.0-beta.97", "ajv": "^8.17.1", "async-mutex": "^0.5.0", "cross-spawn": "^7.0.6", "detect-port": "^1.4.0", "diff": "^7.0.0", "fzf": "^0.5.2", "globby": "^14.0.2", "groq-sdk": "^0.9.0", "http-proxy-agent": "7.0.0", "https-proxy-agent": "7.0.2", "jschardet": "3.1.4", "kerberos": "2.1.1", "lucide-react": "^0.460.0", "minimist": "^1.2.6", "mkdirp": "^3.0.1", "native-is-elevated": "0.7.0", "native-keymap": "^3.3.5", "native-watchdog": "^1.4.1", "node-pty": "1.1.0-beta22", "ollama": "^0.5.11", "open": "^8.4.2", "openai": "^4.76.1", "os-name": "^6.0.0", "ping": "^0.4.4", "posthog-node": "^4.3.1", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-syntax-highlighter": "^15.6.1", "react-use": "^17.6.0", "request": "^2.88.2", "socket.io-client": "^4.7.5", "tas-client-umd": "0.2.0", "ts-lsp-client": "1.0.3", "turndown": "^7.2.0", "unzipper": "^0.10.14", "v8-inspect-profiler": "^0.1.1", "vscode-oniguruma": "1.7.0", "vscode-regexpp": "^3.1.0", "vscode-textmate": "9.2.0", "yauzl": "^3.0.0", "yazl": "^2.4.3", "zod": "^3.24.1"}, "devDependencies": {"@playwright/test": "^1.50.0", "@stylistic/eslint-plugin-ts": "^2.8.0", "@types/cookie": "^0.3.3", "@types/debug": "^4.1.5", "@types/detect-port": "^1.3.5", "@types/diff": "^6.0.0", "@types/eslint": "^9.6.1", "@types/gulp-svgmin": "^1.2.1", "@types/http-proxy-agent": "^2.0.1", "@types/kerberos": "^1.1.2", "@types/minimist": "^1.2.1", "@types/mkdirp": "^1.0.2", "@types/mocha": "^9.1.1", "@types/node": "20.x", "@types/ping": "^0.4.4", "@types/qrcode.react": "^3.0.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-syntax-highlighter": "^15.5.13", "@types/request": "^2.48.12", "@types/sinon": "^10.0.2", "@types/sinon-test": "^2.4.2", "@types/trusted-types": "^1.0.6", "@types/turndown": "^5.0.5", "@types/unzipper": "^0.10.7", "@types/vscode-notebook-renderer": "^1.72.0", "@types/webpack": "^5.28.5", "@types/wicg-file-system-access": "^2020.9.6", "@types/windows-foreground-love": "^0.3.0", "@types/winreg": "^1.2.30", "@types/yauzl": "^2.10.0", "@types/yazl": "^2.4.2", "@typescript-eslint/utils": "^8.8.0", "@vscode/gulp-electron": "1.36.0", "@vscode/l10n-dev": "0.0.35", "@vscode/telemetry-extractor": "^1.10.2", "@vscode/test-cli": "^0.0.6", "@vscode/test-electron": "^2.4.0", "@vscode/test-web": "^0.0.62", "@vscode/v8-heap-parser": "^0.1.0", "@vscode/vscode-perf": "^0.0.19", "@webgpu/types": "^0.1.44", "ansi-colors": "^3.2.3", "asar": "^3.0.3", "chromium-pickle-js": "^0.2.0", "cookie": "^0.7.2", "copy-webpack-plugin": "^11.0.0", "css-loader": "^6.9.1", "cssnano": "^6.0.3", "debounce": "^1.0.0", "deemon": "^1.8.0", "electron": "^32.2.7", "eslint": "^9.11.1", "eslint-formatter-compact": "^8.40.0", "eslint-plugin-header": "3.1.1", "eslint-plugin-jsdoc": "^50.3.1", "eslint-plugin-local": "^6.0.0", "event-stream": "3.3.4", "fancy-log": "^1.3.3", "file-loader": "^6.2.0", "glob": "^5.0.13", "gulp": "^4.0.0", "gulp-azure-storage": "^0.12.1", "gulp-bom": "^3.0.0", "gulp-buffer": "0.0.2", "gulp-filter": "^5.1.0", "gulp-flatmap": "^1.0.2", "gulp-gunzip": "^1.0.0", "gulp-gzip": "^1.4.2", "gulp-json-editor": "^2.5.0", "gulp-plumber": "^1.2.0", "gulp-rename": "^1.2.0", "gulp-replace": "^0.5.4", "gulp-sourcemaps": "^3.0.0", "gulp-svgmin": "^4.1.0", "gulp-untar": "^0.0.7", "husky": "^0.13.1", "innosetup": "6.0.5", "istanbul-lib-coverage": "^3.2.0", "istanbul-lib-instrument": "^6.0.1", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.1.5", "lazy.js": "^0.4.2", "marked": "^15.0.0", "merge-options": "^1.0.1", "mime": "^1.4.1", "minimatch": "^3.0.4", "minimist": "^1.2.6", "mocha": "^10.2.0", "mocha-junit-reporter": "^2.2.1", "mocha-multi-reporters": "^1.5.1", "next": "^15.1.4", "nodemon": "^3.1.9", "npm-run-all": "^4.1.5", "original-fs": "^1.2.0", "os-browserify": "^0.3.0", "p-all": "^1.0.0", "path-browserify": "^1.0.1", "postcss": "^8.4.33", "postcss-nesting": "^12.0.2", "pump": "^1.0.1", "rcedit": "^1.1.0", "rimraf": "^2.7.1", "scope-tailwind": "^1.0.5", "shiki": "^3.4.0", "sinon": "^12.0.1", "sinon-test": "^3.1.3", "source-map": "0.6.1", "source-map-support": "^0.3.2", "style-loader": "^3.3.2", "tailwindcss": "^3.4.14", "ts-loader": "^9.5.1", "ts-node": "^10.9.1", "tsec": "0.2.7", "tslib": "^2.6.3", "tsup": "^8.3.5", "typescript": "^5.8.0-dev.20250121", "typescript-eslint": "^8.8.0", "util": "^0.12.4", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-stream": "^7.0.0", "xml2js": "^0.5.0", "yaserver": "^0.4.0"}, "overrides": {"node-gyp-build": "4.8.1", "kerberos@2.1.1": {"node-addon-api": "7.1.0"}}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}, "bugs": {"url": "https://github.com/microsoft/vscode/issues"}, "optionalDependencies": {"windows-foreground-love": "0.5.0"}, "contributes": {"configuration": {"properties": {"workbench.view.chat.visible": {"type": "boolean", "default": false, "description": "Controls visibility of the chat view"}, "workbench.view.edits.visible": {"type": "boolean", "default": false, "description": "Controls visibility of the edits view"}}}}}