# 绿色装饰一致性服务实现测试

## 修改总结

我们已经成功将绿色装饰的实现从直接添加到模型改为使用一致性服务，确保绿色装饰和红色装饰使用相同的机制。

### 主要修改内容：

1. **扩展Diff接口**：
   - 添加了`greenDecorationId`、`redDecorationId`和`widgetId`字段来存储一致性服务ID

2. **修改绿色装饰实现**：
   - 将`_addLineDecoration`的直接调用改为使用`_consistentItemService.addConsistentItemToURI`
   - 保存一致性服务ID到diff对象中

3. **修改红色装饰实现**：
   - 保存一致性服务ID到diff对象中（原本已经使用一致性服务）

4. **修改Accept/Reject widget实现**：
   - 保存一致性服务ID到diff对象中

5. **修改清理逻辑**：
   - 在`_deleteDiff`方法中添加了清理一致性服务项的逻辑

## 预期效果

修改后，绿色装饰将具有与红色装饰相同的行为：
- 当文件关闭后重新打开时，绿色装饰会自动重新显示
- 装饰与URI绑定，而不是与特定的编辑器或模型实例绑定
- 当编辑器重新创建时会自动重新应用装饰

## 测试建议

1. **基本功能测试**：
   - 创建一些diff区域，确认绿色和红色装饰都正常显示
   - 关闭文件后重新打开，确认装饰仍然存在

2. **清理测试**：
   - Accept或Reject diff，确认装饰被正确清理
   - 删除整个diff区域，确认所有相关装饰都被清理

3. **多文件测试**：
   - 在多个文件中创建diff，切换文件，确认装饰在正确的文件中显示

## 技术细节

使用一致性服务的优势：
- **持久性**：装饰与URI绑定，编辑器重新创建时自动恢复
- **一致性**：绿色和红色装饰使用相同的机制
- **自动管理**：一致性服务自动处理编辑器的生命周期

这个修改解决了绿色装饰在文件重新打开时丢失的问题，确保了用户体验的一致性。
